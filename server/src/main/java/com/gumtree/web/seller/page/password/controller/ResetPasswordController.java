package com.gumtree.web.seller.page.password.controller;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.google.common.collect.Lists;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.bapi.model.PasswordKey;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.ChangePasswordRequest;
import com.gumtree.user.service.model.GumtreeAccessToken;
import com.gumtree.user.service.model.VerificationKeyType;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.password.api.UserApiService;
import com.gumtree.web.seller.page.password.model.UpdatePasswordForm;
import com.gumtree.web.seller.page.password.model.UpdatePasswordModel;
import com.gumtree.web.seller.page.password.model.UpdatePasswordResult;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.password.PasswordResetAttempt;
import com.gumtree.zeno.core.event.user.sellerside.password.PasswordResetFail;
import com.gumtree.zeno.core.event.user.sellerside.password.PasswordResetSuccess;
import com.gumtree.zeno.core.service.ZenoService;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import static com.codahale.metrics.MetricRegistry.name;
import static com.gumtree.web.seller.page.password.controller.ResetPasswordController.PAGE_PATH;

/**
 * Reset password controller to handle the url from a client returning to the site after they have clicked a link in
 * their email.
 */
@Controller
@GoogleAnalytics
@GumtreePage(PageType.PasswordReset)
@RequestMapping(PAGE_PATH)
public class ResetPasswordController extends BaseSellerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResetPasswordController.class);

    public static final String PAGE_PATH = "/reset-password";
    public static final String SECURE_PAGE_PATH = "/reset-password/reset";
    protected static final String USER_ID_PARAM = "id";
    protected static final String PASSWORD_RESET_KEY_PARAM = "key";
    public static final String RESET_SUB_PATH = "/reset";
    public static final String EXPERIMENT_RESET_SUB_PATH = "/submit-reset";
    public static final String MISSING_PARAMS_COUNTER = "ResetPassword-MissingParamsCounter";
    private static final String ENCRYPTED_MAP_PARAMETER_NAME = "gt_d";
    private static final String RELOAD_PARAM="reload";

    public static final String EXPERIMENT_RESET_PASSWORD_VIEW_PATH = "/view";

    private final ZenoService zenoService;
    private final LoginUtils loginUtils;
    private final UserServiceFacade userServiceFacade;
    private final Counter missingParamsCounter;
    private final ParameterEncryption parameterEncryption;
    private final UserApiService userControllerApiService;

    @Autowired
    public ResetPasswordController(CookieResolver cookieResolver,
                                   CategoryModel categoryModel,
                                   ApiCallExecutor apiCallExecutor,
                                   ErrorMessageResolver messageResolver,
                                   UrlScheme urlScheme,
                                   ZenoService zenoService,
                                   LoginUtils loginUtils,
                                   UserSessionService userSessionService,
                                   UserServiceFacade userServiceFacade,
                                   MetricRegistry metricRegistry,
                                   ParameterEncryption parameterEncryption,
                                   UserApiService userControllerApiService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.zenoService = zenoService;
        this.loginUtils = loginUtils;
        this.userServiceFacade = userServiceFacade;
        this.parameterEncryption = parameterEncryption;
        this.userControllerApiService = userControllerApiService;
        this.missingParamsCounter = metricRegistry.counter(name(ResetPasswordController.class, MISSING_PARAMS_COUNTER));
    }

    // Due to security ticket https://jira.corp.ebay.com/browse/SEC-9276
    // we reload the /reset-password/reset page with a POST auto submit upon load
    // This is to hide the parameters that was passed in the URL in the HTTP body so
    // it cannot be seen in the referrer parameter when clicking on an external link
    @RequestMapping(method = RequestMethod.GET)
    public final ModelAndView showPageWithUrlParams(@RequestParam(value = PASSWORD_RESET_KEY_PARAM, required = false) String passwordKey,
                                                    HttpServletRequest request) {
        if(StringUtils.isNotBlank(passwordKey)) {
            UpdatePasswordModel.Builder resetPasswordModel = UpdatePasswordModel.builder()
                    .withPage(Page.ResetPassword)
                    .withUpdatePasswordBean(new UpdatePasswordForm())
                    .withKey(encodeParam(passwordKey))
                    .withReload(true)
                    .withActionName("Reset");

            CoreModel.Builder coreModel = getCoreModelBuilder(request);
            ModelAndView modelAndView = resetPasswordModel.build(coreModel);

            return modelAndView;

        } else {
            LOGGER.info("failed attempt to reset password with email or key");
            // send them back to forgotten password page
            return redirect(ForgottenPasswordController.PAGE_PATH);
        }
    }

    @RequestMapping(method = RequestMethod.POST)
    public final ModelAndView showPage(HttpServletRequest request) throws IOException {

        String passwordKeyEncoded = request.getParameter("form.k");
        if (!StringUtils.isNotBlank(passwordKeyEncoded)) {
            missingParamsCounter.inc();
            return errorResponse(request);
        }
        String passwordKey = new String(decodeParam(passwordKeyEncoded));
        PasswordKey responseKey = userControllerApiService.getPasswordKey(passwordKey);

        if (responseKey == null) {
            return errorResponse(request);
        }
        String username = responseKey.getUsername();
        String formAction = buildFormAction(getUrlScheme().urlFor(Actions.BUSHFIRE_RESET_PASSWORD),
                encodeParam(username), encodeParam(passwordKey));

        zenoService.logEvent(new String[0], Page.ResetPassword.getTemplateName(), PasswordResetAttempt.class);

        CoreModel.Builder coreModel = getCoreModelBuilder(request);
        coreModel.withGaEvents(Lists.newArrayList(PasswordResetAttempt.class.getSimpleName()));
        UpdatePasswordModel.Builder resetPasswordModel = UpdatePasswordModel.builder()
                .withPage(Page.ResetPassword)
                .withUpdatePasswordBean(new UpdatePasswordForm())
                .withId(encodeParam(username))
                .withKey(encodeParam(passwordKey))
                .withReload(false)
                .withActionName("Reset")
                .withFormAction(formAction);

        return resetPasswordModel.build(coreModel);
    }

    // This has been created to support Login and Registration improvement experiment GTC-2229
    @RequestMapping(value = EXPERIMENT_RESET_PASSWORD_VIEW_PATH, method = RequestMethod.POST)
    public final ResponseEntity<UpdatePasswordResult> showResetPasswordPage(HttpServletRequest request)
            throws IOException {

        String passwordKeyEncoded = request.getParameter("form.k");
        if (!StringUtils.isNotBlank(passwordKeyEncoded)) {
            missingParamsCounter.inc();
            return new ResponseEntity<>(buildUpdatePasswordResultWithError(), HttpStatus.BAD_REQUEST);
        }
        String passwordKey = new String(decodeParam(passwordKeyEncoded));
        PasswordKey responseKey = userControllerApiService.getPasswordKey(passwordKey);

        if (responseKey == null) {
            return new ResponseEntity<>(buildUpdatePasswordResultWithError(), HttpStatus.BAD_REQUEST);
        }
        String username = responseKey.getUsername();
        String formAction = buildFormAction(getUrlScheme().urlFor(Actions.BUSHFIRE_RESET_PASSWORD),
                encodeParam(username), encodeParam(passwordKey));

        zenoService.logEvent(new String[0], Page.ResetPassword.getTemplateName(), PasswordResetAttempt.class);

        UpdatePasswordResult updatePasswordResult = UpdatePasswordResult.builder()
                .withPage(Page.ResetPassword)
                .withUpdatePasswordBean(new UpdatePasswordForm())
                .withId(encodeParam(username))
                .withKey(encodeParam(passwordKey))
                .withReload(false)
                .withActionName("Reset")
                .withFormAction(formAction)
                .build();

        return new ResponseEntity<>(updatePasswordResult, HttpStatus.OK);
    }

    /**
     * Method to handle form submission from the Choose new password page
     *
     * @return success -> manage ads, validation errors -> reset-password -> other errors -> error-reset-password
     * @throws IOException throws an exception
     */
    @RequestMapping(value = RESET_SUB_PATH, method = RequestMethod.POST)
    public final ModelAndView resetPassword(Subject subject,
                            RedirectAttributes redirectAttributes,
                            HttpServletRequest request,
                            @RequestParam(value = "form.k") String passwordKeyEncoded,
                            @RequestParam(value = "form.u") String usernameEncoded,
                            @RequestParam(value = "form.password", required = false) String password,
                            @RequestParam(value = "form.confirmedPassword", required = false) String confirmedPassword)
            throws IOException {

        if (!isValidParameters(passwordKeyEncoded, usernameEncoded)) {
            return errorResponse(request);
        }

        String username = new String(decodeParam(usernameEncoded));
        String passwordKey = new String(decodeParam(passwordKeyEncoded));
        ChangePasswordRequest req = new ChangePasswordRequest();
        req.setUsername(username);
        req.setVerificationKeyType(VerificationKeyType.RESET_PASSWORD_KEY);
        req.setVerificationKey(passwordKey);
        req.setPassword(password);
        req.setConfirmedPassword(confirmedPassword);

        ApiResponse<GumtreeAccessToken> resp = userServiceFacade.changePassword(req);

        if (resp.isDefined()) {
            loginUtils.login(subject, username, password);
            redirectAttributes.addFlashAttribute("successNotice", resolveMessage("password.reset.success"));

            zenoService.logEvent(new String[0], Page.ResetPassword.getTemplateName(), PasswordResetSuccess.class);
            return redirect(getUrlScheme().urlFor(Actions.BUSHFIRE_MANAGE_ADS));

        } else {
            String formAction = buildFormAction(getUrlScheme().urlFor(Actions.BUSHFIRE_RESET_PASSWORD),username, passwordKey);

            UpdatePasswordForm updatePasswordForm = new UpdatePasswordForm();
            updatePasswordForm.setKey(passwordKey);

            CoreModel.Builder coreModel = getCoreModelBuilder(request);
            coreModel.withGaEvents(Lists.newArrayList(PasswordResetFail.class.getSimpleName()));
            UpdatePasswordModel.Builder updatePasswordModel = UpdatePasswordModel.builder()
                    .withPage(Page.ResetPassword)
                    .withValidationErrors()
                    .withId(usernameEncoded)
                    .withKey(passwordKeyEncoded)
                    .withActionName("Reset")
                    .withFormAction(formAction)
                    .withUpdatePasswordBean(updatePasswordForm)
                    .withLoginLink(getUrlScheme().urlFor(Actions.BUSHFIRE_LOGIN))
                    .withForgottenPasswordLink(getUrlScheme().urlFor(Actions.BUSHFIRE_FORGOTTEN_PASSWORD));

            zenoService.logEvent(new String[0], Page.ResetPassword.getTemplateName(), PasswordResetFail.class);
            return updatePasswordModel.build(coreModel);
        }
    }

    // This has been created to support Login and Registration improvement experiment GTC-2229
    @RequestMapping(value = EXPERIMENT_RESET_SUB_PATH, method = RequestMethod.POST)
    public final ResponseEntity<UpdatePasswordResult> submitResetPassword(Subject subject,
                                            HttpServletRequest request,
                                            @RequestParam(value = "form.k") String passwordKeyEncoded,
                                            @RequestParam(value = "form.u") String usernameEncoded,
                                            @RequestParam(value = "form.password", required = false) String password,
                                            @RequestParam(value = "form.confirmedPassword", required = false) String confirmedPassword)
            throws IOException {

        if (!isValidParameters(passwordKeyEncoded, usernameEncoded)) {
            return new ResponseEntity<>(buildUpdatePasswordResultWithError(), HttpStatus.BAD_REQUEST);
        }

        String username = new String(decodeParam(usernameEncoded));
        String passwordKey = new String(decodeParam(passwordKeyEncoded));
        ChangePasswordRequest req = new ChangePasswordRequest();
        req.setUsername(username);
        req.setVerificationKeyType(VerificationKeyType.RESET_PASSWORD_KEY);
        req.setVerificationKey(passwordKey);
        req.setPassword(password);
        req.setConfirmedPassword(confirmedPassword);

        ApiResponse<GumtreeAccessToken> resp = userServiceFacade.changePassword(req);

        if (resp.isDefined()) {
            loginUtils.login(subject, username, password);
            zenoService.logEvent(new String[0], Page.ResetPassword.getTemplateName(), PasswordResetSuccess.class);
            return new ResponseEntity<>(
                    UpdatePasswordResult.builder()
                            .withPage(Page.ResetPassword)
                            .withActionName("Reset")
                            .withResetConfirmationUrl(getUrlScheme().urlFor(Actions.BUSHFIRE_MANAGE_ADS))
                            .build(),
                    HttpStatus.OK);

        } else {
            return new ResponseEntity<>(buildUpdatePasswordResultWithError(), HttpStatus.BAD_REQUEST);
        }
    }

    private boolean isValidParameters(String passwordKeyEncoded, String usernameEncoded) {
        return StringUtils.isNotBlank(usernameEncoded) && StringUtils.isNotBlank(passwordKeyEncoded);
    }

    protected String encodeParam(String param) {
        return Base64.encodeBase64URLSafeString(param.getBytes());
    }

    protected byte[] decodeParam(String param) throws UnsupportedEncodingException {
        return Base64.decodeBase64(URLDecoder.decode(param, "UTF-8"));
    }

    private ModelAndView errorResponse(HttpServletRequest request) {
        CoreModel.Builder coreModel = getCoreModelBuilder(request);
        UpdatePasswordModel.Builder resetPasswordModel = UpdatePasswordModel.builder()
                .withPage(Page.ResetPassword)
                .withValidationErrors()
                .withActionName("Reset")
                .withUpdatePasswordBean(new UpdatePasswordForm())
                .withLoginLink(getUrlScheme().urlFor(Actions.BUSHFIRE_LOGIN))
                .withForgottenPasswordLink(getUrlScheme().urlFor(Actions.BUSHFIRE_FORGOTTEN_PASSWORD));

        zenoService.logEvent(new String[0], Page.ResetPassword.getTemplateName(), PasswordResetFail.class);
        return resetPasswordModel.build(coreModel);
    }

    private String buildFormAction(String path, String userId, String passwordResetKey) {
        return path + "?" + USER_ID_PARAM + "=" + userId + "&" + PASSWORD_RESET_KEY_PARAM + "=" + passwordResetKey;
    }

    private UpdatePasswordResult buildUpdatePasswordResultWithError() {
        zenoService.logEvent(new String[0], Page.ResetPassword.getTemplateName(), PasswordResetFail.class);
        return UpdatePasswordResult.builder()
                .withPage(Page.ResetPassword)
                .withValidationErrors()
                .withActionName("Reset")
                .withUpdatePasswordBean(new UpdatePasswordForm())
                .withLoginLink(getUrlScheme().urlFor(Actions.BUSHFIRE_LOGIN))
                .withForgottenPasswordLink(getUrlScheme().urlFor(Actions.BUSHFIRE_FORGOTTEN_PASSWORD))
                .build();
    }
}
