package com.gumtree.web.seller.page.payment.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.manageads.ManageAdsController;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.service.ZenoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Controller
@GoogleAnalytics
@GumtreePage(PageType.OrderPaymentError)
public final class PaymentFailureController extends BaseSellerController {

    public static final String VIEW_NAME = "payment-failed";

    public static final String PAGE_PATH = "/checkout/failure";

    private final ZenoService zenoService;

    @Autowired
    public PaymentFailureController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                    ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                    UrlScheme urlScheme, ZenoService zenoService, UserSessionService userSessionService
    ) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.zenoService = zenoService;
    }

    /**
     * @return path to manage ads
     */
    @ModelAttribute("manageAdsLink")
    public String manageAdsPageLink() {
        return ManageAdsController.PAGE_PATH;
    }

    /**
     * Display generic error page on payment failure
     *
     * @param model    - model containing transaction reference
     * @param response - servlet response
     * @return view
     * @throws IOException on failure to set 404
     */
    @RequestMapping(PAGE_PATH)
    public String displayPaymentError(Model model, HttpServletResponse response) throws IOException {
        if (model.containsAttribute("transactionRef")) {
            zenoService.logBackendEvent("PaymentFailure", PageType.Error_404, "transaction-ref-payment-failed", model);
            return VIEW_NAME;
        }
        zenoService.logBackendEvent("PaymentFailure", PageType.Error_404, "payment-failed", model);
        return fail(response);
    }

    private String fail(HttpServletResponse response) throws IOException {
        response.sendError(404);
        return null;
    }
}
