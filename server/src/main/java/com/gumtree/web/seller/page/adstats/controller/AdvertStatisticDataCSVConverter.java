package com.gumtree.web.seller.page.adstats.controller;

import com.gumtree.web.seller.service.adstats.AdvertStatisticData;
import com.gumtree.web.view.CSVEntryConverter;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Arrays;
import java.util.List;

public class AdvertStatisticDataCSVConverter implements CSVEntryConverter<AdvertStatisticData> {

    private static DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("dd/MM/yyyy HH:mm");

    private static List<String> columnNames = Arrays.asList(
            "Ad ID",
            "Ad title",
            "Link to ad",
            "Ad status",
            "Contact email",
            "Creation date",
            "Last posted date",
            "Last modified date",
            "Number of times re-posted",
            "Listing views",
            "Full ad views",
            "Number of replies"
    );

    @Override
    public List<String> getColumnNames() {
        return columnNames;
    }

    @Override
    public String[] convertEntry(AdvertStatisticData adStatsData) {
        String[] values = new String[12];
        values[0] = adStatsData.getAdvertId();
        values[1] = adStatsData.getAdvertTitle();
        values[2] = adStatsData.getAdvertVIPUrl();
        values[3] = adStatsData.getAdvertStatus() != null ? adStatsData.getAdvertStatus().getDisplayValue() : null;
        values[4] = adStatsData.getContactEmail();
        values[5] = formatDate(adStatsData.getCreationDate());
        values[6] = formatDate(adStatsData.getLastPostedDate());
        values[7] = formatDate(adStatsData.getLastModifiedDate());
        values[8] = formatNumber(adStatsData.getNumberOfTimesReposted());
        values[9] = formatNumber(adStatsData.getSRPViews());
        values[10] = formatNumber(adStatsData.getVIPViews());
        values[11] = formatNumber(adStatsData.getNumberOfReplies());
        replaceNullsWithEmptyStringsInArray(values);
        return values;
    }

    private void replaceNullsWithEmptyStringsInArray(String[] values) {
        for (int i = 0; i < values.length; i++) {
            if (values[i] == null) {
                values[i] = "";
            }
        }
    }

    private String formatDate(DateTime dateTime) {
        return dateTime == null ? "" : dateTimeFormatter.print(dateTime);
    }

    private String formatNumber(Number number) {
        return number == null ? "" : number.toString();
    }
}
