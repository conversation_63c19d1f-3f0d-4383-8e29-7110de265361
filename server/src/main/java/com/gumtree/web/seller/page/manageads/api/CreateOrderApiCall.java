package com.gumtree.web.seller.page.manageads.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;

/**
 * Represents a call to the API to create an order
 */
public final class CreateOrderApiCall extends AuthenticatedApiCall<ApiOrder> {

    private CreateOrderBean createOrderBean;

    /**
     * Constructor.
     *
     * @param createOrderBean order details
     * @param apiKeyProvider the key used to sign the request
     */
    public CreateOrderApiCall(CreateOrderBean createOrderBean, ApiKeyProvider apiKeyProvider) {
        super(apiKeyProvider);
        this.createOrderBean = createOrderBean;
    }

    @Override
    public ApiOrder execute(BushfireApi api) {
        return api.create(OrderApi.class, getApiKey()).createOrder(createOrderBean);
    }

    public CreateOrderBean getCreateOrderBean() {
        return createOrderBean;
    }
}
