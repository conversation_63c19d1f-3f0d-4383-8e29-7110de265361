package com.gumtree.web.seller.page.payment.util;

import com.gumtree.api.AdStatus;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.postad.model.meta.PageActionType;
import com.gumtree.web.seller.page.postad.model.meta.PagePaymentType;
import com.gumtree.web.seller.page.postad.model.products.ProductType;

import java.util.List;

/**
 * Created by mdivilioglu on 6/15/17.
 */
public interface MetaDataGenerator {
    /*
    * This is to find out if the operation is POST, RELIST or UPDATE.
    * original Status is usually retrieved as an extra information in case of an
    * editoral update.
    * */
    PageActionType getPageAction(Checkout checkout, AdStatus originalStatus);

    /* This is to generate the payment behaviour of the purchase. Normally
    * it is either an INSERTION or a FEATURE payment.
    * */
    List<PagePaymentType> getPaymentTypes(Checkout checkout);

    /*
    * This is to generate the selected feature types. In case of multiple ads
    * the features include all the futures that has been purchased for all the ads.
    * However they are represented once even if the same feature is purchased by
    * multiple ads.
    * */
    List<ProductType> getPageFeatureTypes(Checkout checkout);
}
