package com.gumtree.web.seller.page.reviews.service;

import com.google.gson.Gson;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.fulladsearch.FullAdsSearchApi;
import com.gumtree.fulladsearch.model.FullAdEqualInstruction;
import com.gumtree.fulladsearch.model.FullAdSearchInstruction;
import com.gumtree.fulladsearch.model.FullAdSearchRequest;
import com.gumtree.fulladsearch.model.FullAdSearchableField;
import com.gumtree.tns.api.ReviewConnectionsManagementApi;
import com.gumtree.tns.api.ReviewDisplayApi;
import com.gumtree.tns.dto.*;
import com.gumtree.web.seller.page.reviews.model.ExternalReviewManagement;
import com.gumtree.web.seller.page.reviews.model.google.*;
import com.gumtree.web.seller.page.reviews.model.google.oauth.GoogleOAuthWebRequest;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.Single;
import rx.functions.Func1;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

public class ExternalReviewsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ExternalReviewsService.class);

    private final ReviewConnectionsManagementApi externalReviewConnectionsManagementApi;

    private final ReviewDisplayApi externalReviewDisplayApi;

    private final FullAdsSearchApi fullAdsSearchApi;

    private final CategoryModel categoryModel;

    public ExternalReviewsService(ReviewConnectionsManagementApi externalReviewConnectionsManagementApi, ReviewDisplayApi externalReviewDisplayApi, FullAdsSearchApi fullAdsSearchApi, CategoryModel categoryModel) {
        this.externalReviewConnectionsManagementApi = externalReviewConnectionsManagementApi;
        this.externalReviewDisplayApi = externalReviewDisplayApi;
        this.fullAdsSearchApi = fullAdsSearchApi;
        this.categoryModel = categoryModel;
    }

    public AsyncResult doGoogleAuth(Long selectedAccountId, GoogleOAuthWebRequest request) {

        GbpAuthorizationRequest gbpAuthorizationRequest = new GbpAuthorizationRequest();
        gbpAuthorizationRequest
                .accountId(String.valueOf(selectedAccountId))
                .authorizationCode(request.getAuthCode())
                .redirectUri(request.getRedirectUrl())
                .clientId(request.getClientId())
                .codeVerifier(request.getCodeVerifier())
                .clientIdType(ClientIdTypeEnum.WEB)
                .accessPlatform(AccessPlatformEnum.PC)//m = pc temp
        ;
        return externalReviewConnectionsManagementApi.authorizeReviewSource(ReviewProviderEnum.GBP, gbpAuthorizationRequest)
                .map(this::toAsyncResult)
                .toBlocking().value();
    }

    private AsyncResult toAsyncResult(OperationResult e) {
        AsyncResult asyncResult = new AsyncResult();
        asyncResult.setMessage(e.getMessage());
        if (e.getStatus() != null) {
            asyncResult.setStatus(e.getStatus().getValue());
        }
        if (e.getTimestamp() != null) {
            asyncResult.setTimestamp(e.getTimestamp().toInstant().toEpochMilli());
        }
        if (e.getEstimatedCompletionTime() != null) {
            asyncResult.setEstimatedCompletionTime(e.getEstimatedCompletionTime().toInstant().toEpochMilli());
        }
        asyncResult.setOperationId(e.getOperationId());
        return asyncResult;
    }

    public String setGoogleDisplay(Long selectedAccountId, String relationId, Integer display) {
        UpdateDisplayStatusRequest updateDisplayStatusRequest = new UpdateDisplayStatusRequest();
        updateDisplayStatusRequest.setDisplayStatus(new Integer(1).equals(display) ? DisplayStatusEnum.DISPLAYED : DisplayStatusEnum.NOT_DISPLAYED);
        Single<ThirdReviewAccountRelationOutput> thirdReviewAccountRelationOutputSingle = externalReviewDisplayApi.updateDisplayStatus(String.valueOf(selectedAccountId), Long.parseLong(relationId), updateDisplayStatusRequest);
        return thirdReviewAccountRelationOutputSingle
                .map(e -> String.valueOf(e.getId()))
                .toBlocking().value();
    }

    public AsyncResult reSyncGoogleData(Long selectedAccountId, String authid) {
        return externalReviewConnectionsManagementApi.syncRelation(String.valueOf(selectedAccountId), Long.parseLong(authid))
                .map(this::toAsyncResult)
                .toBlocking().value();
    }

    public AsyncResult unBind(Long selectedAccountId, String authid) {
        return externalReviewConnectionsManagementApi.unbindRelation(String.valueOf(selectedAccountId), ReviewProviderEnum.GBP)
                .map(this::toAsyncResult)
                .toBlocking().value();
    }

    public GoogleRelationListResponse getGoogleRelationList(Long selectedAccountId, String authid) {
        Single<List<ThirdReviewAccountRelationOutput>> relationChoices = externalReviewConnectionsManagementApi.getRelationChoices(String.valueOf(selectedAccountId), ReviewProviderEnum.GBP, Long.parseLong(authid));
        return relationChoices
                .map(relations -> {
                    GoogleRelationListResponse response = new GoogleRelationListResponse();
                    List<GoogleRelationItem> items = relations.stream()
                            .map(relation -> {
                                GoogleRelationItem item = new GoogleRelationItem();
                                item.setRelationId(String.valueOf(relation.getId()));
                                item.setBusinessName(relation.getNameRedundant());
                                return item;
                            })
                            .collect(Collectors.toList());
                    response.setRelations(items);
                    return response;
                })
                .toBlocking()
                .value();
    }

    public String setGoogleRelation(Long selectedAccountId, String relationId) {
        return externalReviewConnectionsManagementApi.setEffectiveRelation(String.valueOf(selectedAccountId), Long.parseLong(relationId))
                .map(e -> String.valueOf(e.getId()))
                .toBlocking().value();
    }

    public boolean isService(Long l1Category) {
        return Objects.equals(l1Category, Categories.SERVICES.getId());
    }

    public ExternalReviewManagement getSummary(Long accountId, boolean isMyDetailPage) {

        Single<AccountOauthInfoOutput> activeOauthInfoOpt = externalReviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP)
                .onErrorReturn(handleError());
        Single<ThirdReviewAccountRelationWithInfoOutput> effectiveRelationWithInfoOpt = externalReviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP).onErrorReturn(e -> null);
        Single<Boolean> postSpecialCategoryOpt = isPostSpecialCategory(accountId, Categories.SERVICES.getId()).onErrorReturn(e -> false);

        return Single.zip(activeOauthInfoOpt, effectiveRelationWithInfoOpt, postSpecialCategoryOpt, (activeOauthInfo, effectiveRelationWithInfo, postSpecialCategory) -> {
            GoogleReviews googleReviews = new GoogleReviews();
            if (!checkIfShowItem(accountId, postSpecialCategory, activeOauthInfo, isMyDetailPage)) {
                googleReviews.setStatus(GoogleReviewStatus.DO_NOT_NEED_SHOW.getValue());
                return googleReviews;
            }

            if (activeOauthInfo == null) {
                googleReviews.setStatus(GoogleReviewStatus.NO_AUTHORIZATION.getValue());
                return googleReviews;
            }
            if (ArrayUtils.contains(DATA_SYNC_ING, activeOauthInfo.getDataSyncStatus())) {
                googleReviews.setStatus(GoogleReviewStatus.DATA_SYNCING.getValue());
                googleReviews.setAuth(toGoogleReviewsAuth(activeOauthInfo));
                return googleReviews;
            }
            if (effectiveRelationWithInfo == null || effectiveRelationWithInfo.getRelation() == null) {
                googleReviews.setStatus(GoogleReviewStatus.NO_MERCHANT_INFO.getValue());
                googleReviews.setAuth(toGoogleReviewsAuth(activeOauthInfo));
                return googleReviews;
            }
            int relationCount = externalReviewConnectionsManagementApi.countRelations(String.valueOf(accountId), ReviewProviderEnum.GBP, activeOauthInfo.getId())
                    .map(RelationCountOutput::getCount).toBlocking().value();
            googleReviews.setStatus(GoogleReviewStatus.OK.getValue());
            googleReviews.setAuth(toGoogleReviewsAuth(activeOauthInfo));
            googleReviews.setSummary(toGoogleReviewSummary(effectiveRelationWithInfo, relationCount));
            return googleReviews;
        }).map(ExternalReviewManagement::new).toBlocking().value();
    }

    private boolean checkIfShowItem(Long accountId, Boolean postSpecialCategory, AccountOauthInfoOutput activeOauthInfo, boolean isMyDetailPage) {
        if (!isMyDetailPage && activeOauthInfo == null) {
            return true;
        }
        if (isMyDetailPage && (postSpecialCategory != null && postSpecialCategory)) {
            return true;
        }
        return false;
    }

    private GoogleReviewsSummary toGoogleReviewSummary(ThirdReviewAccountRelationWithInfoOutput relationWithInfoOutput, int relationCount) {
        GoogleReviewsSummary googleReviewsSummary = new GoogleReviewsSummary();
        ThirdReviewAccountRelationOutput relation = relationWithInfoOutput.getRelation();
        ThirdReviewInfoOutput info = relationWithInfoOutput.getInfo();

        if (relation != null) {
            googleReviewsSummary.setRelationId(String.valueOf(relation.getId()));
            googleReviewsSummary.setBusinessName(relation.getNameRedundant());
            googleReviewsSummary.setDisplaySelected(relation.getDisplayStatus() == DisplayStatusEnum.DISPLAYED ? 1 : 0);
            googleReviewsSummary.setTotalNum("0");
            googleReviewsSummary.setAvgRate("0");
        }
        if (info != null) {
            googleReviewsSummary.setTotalNum(getStringOrDefault( info.getTotalReviews()));
            googleReviewsSummary.setAvgRate(getStringOrDefault(info.getAvgRating()));
            googleReviewsSummary.setBusinessName(info.getName());
        }
        googleReviewsSummary.setHasMultiRelation(relationCount > 1 ? 1 : 0);
        return googleReviewsSummary;
    }

    private static String getStringOrDefault(Object totalReviews) {
        return Optional.ofNullable(totalReviews) // 1. 将可能为null的对象包装成Optional
                .map(String::valueOf)           // 2. 如果对象不为null，则将其转换为字符串
                .orElse("0");
    }

    private GoogleReviewsAuth toGoogleReviewsAuth(AccountOauthInfoOutput activeOauthInfo) {
        GoogleReviewsAuth googleReviewsAuth = new GoogleReviewsAuth();
        googleReviewsAuth.setAuthid(String.valueOf(activeOauthInfo.getId()));
        return googleReviewsAuth;
    }

    private static final DataSyncStatusEnum[] DATA_SYNC_ING = new DataSyncStatusEnum[]{DataSyncStatusEnum.PENDING, DataSyncStatusEnum.IN_PROGRESS};

    private <T> Func1<Throwable, T> handleError() {
        return throwable -> {
            LOGGER.error("invoke ExternalReviewsService error", throwable);
            throw new RuntimeException(throwable);
        };
    }

    public Boolean getGoogleReviewSwitchInSyiFlow(Long selectedAccountId, Long currentPostCateId) {

        if(!isService(categoryModel.getL1CategoryFor(currentPostCateId).transform(Category::getId).or(0L))){
            return Boolean.FALSE;
        }

        if (isBindGoogleReview(selectedAccountId).timeout(100, TimeUnit.MILLISECONDS).toBlocking().value()) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public void getGoogleReviewMyDetailFlow(Long selectedAccountId, TriConsumer<Boolean, Boolean, Boolean> triConsumer) {
        try{
            Single.zip(isPostSpecialCategory(selectedAccountId, Categories.SERVICES.getId()), isBindGoogleReview(selectedAccountId),
                            (isPostSpecialCategory, isBindGoogleReview) -> {
                                triConsumer.accept(
                                        getGoogleReviewSwitch(isPostSpecialCategory, isBindGoogleReview),
                                        isBindGoogleReview || (isPostSpecialCategory != null && isPostSpecialCategory),
                                        isBindGoogleReview
                                );
                                return true;
                            }).timeout(200, TimeUnit.MILLISECONDS)
                    .onErrorReturn(e -> false).toBlocking().value();
        }catch (Exception e){
            LOGGER.error("getGoogleReviewMyDetailFlow error:{}", selectedAccountId, e);
        }
    }

    public Single<Boolean> getGoogleReviewSwitchAsync(Long selectedAccountId) {
        try{
            return Single.zip(isPostSpecialCategory(selectedAccountId, Categories.SERVICES.getId())
                    , isBindGoogleReview(selectedAccountId)
                    , ExternalReviewsService::getGoogleReviewSwitch).timeout(100, TimeUnit.MILLISECONDS).onErrorReturn(e -> Boolean.FALSE);
        }catch (Exception e){
            LOGGER.error("getGoogleReviewSwitchAsync error:{}", selectedAccountId, e);
        }
        return Single.just(Boolean.FALSE);
    }

    private static Boolean getGoogleReviewSwitch(Boolean isPostSpecialCategory, Boolean isBindGoogleReview) {
        if (Boolean.TRUE.equals(isBindGoogleReview)) {
            return Boolean.FALSE;
        }

        if (Boolean.TRUE.equals(isPostSpecialCategory)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private Single<Boolean> isBindGoogleReview(Long selectedAccountId) {
        return externalReviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(selectedAccountId), ReviewProviderEnum.GBP)
                .onErrorReturn(handleError())
                .map(e -> {
                    if (e != null) {
                        return Boolean.TRUE;
                    }
                    return Boolean.FALSE;
                });
    }

    public Single<Boolean> isPostSpecialCategory(Long accountId, Long category) {
        FullAdSearchRequest adSearchRequest = new FullAdSearchRequest();
        FullAdSearchInstruction accountInstruction = new FullAdSearchInstruction().eq(
                new FullAdEqualInstruction().field(FullAdSearchableField.ACCOUNT_ID.getValue()).value(String.valueOf(accountId)));
        FullAdSearchInstruction categoryInstruction = new FullAdSearchInstruction().eq(
                new FullAdEqualInstruction().field(FullAdSearchableField.CATEGORY_ID.getValue()).value(String.valueOf(category)));
        adSearchRequest.addInstructionsItem((accountInstruction));
        adSearchRequest.addInstructionsItem((categoryInstruction));

        return fullAdsSearchApi.search(adSearchRequest).map(e -> {
            if (e.getTotal() != null) {
                return e.getTotal() > 0;
            }
            return Boolean.FALSE;
        }).onErrorReturn(handleError());
    }
}
