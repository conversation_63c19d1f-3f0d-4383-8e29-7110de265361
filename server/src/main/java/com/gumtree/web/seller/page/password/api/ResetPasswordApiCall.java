package com.gumtree.web.seller.page.password.api;

import com.gumtree.api.PasswordKey;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.client.executor.ApiCall;
import com.gumtree.api.domain.user.beans.ResetPasswordBean;

/**
 *
 */
public class ResetPasswordApiCall implements ApiCall<PasswordKey> {

    private String passwordKey;

    private String userName;

    /**
     * Default constructor
     * @param userName required for validation
     * @param passwordKey required for validation
     */
    public ResetPasswordApiCall(String userName, String passwordKey) {
        this.userName = userName;
        this.passwordKey = passwordKey;
    }

    @Override
    public final PasswordKey execute(BushfireApi api) {
        final ResetPasswordBean resetPasswordBean = new ResetPasswordBean();
        resetPasswordBean.setKey(passwordKey);
        return api.create(UserApi.class).getPasswordKey(userName, resetPasswordBean);
    }
}
