package com.gumtree.web.seller.page.reviews.service;

import com.gumtree.api.HystrixUtils;
import com.gumtree.userreviewsservice.client.exception.ClientException;
import com.gumtree.userreviewsservice.client.model.CreateReviewRequest;
import com.gumtree.userreviewsservice.client.model.ErrorItem;
import com.gumtree.userreviewsservice.client.model.ErrorResponse;
import com.gumtree.util.ExceptionUtils;
import com.gumtree.web.api.WebApiError;
import com.gumtree.web.api.WebApiErrorException;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.common.domain.messagecentre.MessageDirection;
import com.gumtree.web.common.domain.messagecentre.Messages;
import com.gumtree.web.common.domain.messagecentre.Role;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.util.Assert;
import rx.Single;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class UserReviewsFunctions {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserReviewsFunctions.class);
    private static final Integer MAX_CONVERSATION_AGE = 28;

    private UserReviewsFunctions() {
    }

    /**
     * Check if it's allowed to create a review for the given conversation (conversation messages)
     *
     * @param messages the conversation messages
     * @return boolean, <code>true</code> if it's allowed to create a review
     */
    public static boolean isConversationReviewable(Messages messages) {
        // Note: buyerId(converseeId) may not always be available as currently we don't force login to reply on all platforms. We need
        // both buyerId and sellerId so if one is missing should not prompt user to write a review
        return messages.getConverseeId().isPresent()
                && messages.getAdvert().isPresent()
                && countMessages(messages, MessageDirection.INBOUND) >= 2
                && countMessages(messages, MessageDirection.OUTBOUND) >= 2
                && messages.getMessages().size() > 4
                && !isConversationIsOlderThan(messages.getMessages(), MAX_CONVERSATION_AGE);
    }

    public static long countMessages(Messages messages, MessageDirection direction) {
        return messages.getMessages().stream().filter(m -> direction.equals(m.getDirection())).count();
    }

    public static long countSellerMessages(Messages messages) {
        if (Role.Buyer.equals(messages.getUserRole())) {
            return countMessages(messages, MessageDirection.INBOUND);
        } else {
            return countMessages(messages, MessageDirection.OUTBOUND);
        }
    }

    public static long countBuyerMessages(Messages messages) {
        if (Role.Buyer.equals(messages.getUserRole())) {
            return countMessages(messages, MessageDirection.OUTBOUND);
        } else {
            return countMessages(messages, MessageDirection.INBOUND);
        }
    }

    public static CreateReviewRequest.DirectionEnum convert2Direction(Role userRole) {
        Assert.notNull(userRole);
        switch(userRole) {
            case Buyer:
                return CreateReviewRequest.DirectionEnum.B2S;
            case Seller:
                return CreateReviewRequest.DirectionEnum.S2B;
            default:
                // fail hard to get it fix quickly
                throw new RuntimeException("Unknown user role: " + userRole);
        }
    }

    public static WebApiErrorResponse convertErrorResponseToWebApiErrorResponse(HttpStatus status, ErrorResponse errResp) {
        if (errResp.getErrors() != null && !errResp.getErrors().isEmpty()) {
            List<WebApiError> errItems = errResp.getErrors().stream()
                    .map(UserReviewsFunctions::convertErrorItemToWebApiError)
                    .collect(Collectors.toList());

            return new WebApiErrorResponse(status, errResp.getErrorCode(), errResp.getMessage(), errItems);
        } else {
            return new WebApiErrorResponse(status, errResp.getErrorCode(), errResp.getMessage());
        }
    }

    public static boolean isConversationIsOlderThan(List<com.gumtree.web.common.domain.messagecentre.Message> messages, int maxAge) {
        return getConversationAgeInDays(messages).filter(age -> age >= maxAge).isPresent();
    }

    public static Optional<Integer> getConversationAgeInDays(List<com.gumtree.web.common.domain.messagecentre.Message> messages) {
        return getConversationCreationTime(messages).map(creationDate -> Days.daysBetween(creationDate, new DateTime()).getDays());
    }

    public static Optional<Long> getRevieweeId(Messages messages) {
        return messages.getConverseeId();
    }

    public static Single<Long> handleSubmitReviewError(Throwable error) {
        Throwable unwrappedErr = HystrixUtils.unwrapThrowable(error);
        if (unwrappedErr instanceof ClientException) {
            ClientException clientException = (ClientException) unwrappedErr;
            if (clientException.getErrorResponse().isPresent()) {
                return Single.error(new WebApiErrorException(
                        convertErrorResponseToWebApiErrorResponse(HttpStatus.BAD_REQUEST, clientException.getErrorResponse().get())));
            } else {
                LOGGER.error("Unexpected error (handled). Message: " + ExceptionUtils.toShortMessage(unwrappedErr));
                return Single.error(new WebApiErrorException(new WebApiErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR,
                        "review-api-error",
                        "The review api rejected the request for an unknown reason. Check logs for more information.")));
            }
        } else {
            LOGGER.error("Unexpected error (handled). Message: " + ExceptionUtils.toShortMessage(unwrappedErr));
            return Single.error(new WebApiErrorException(new WebApiErrorResponse(
                    HttpStatus.INTERNAL_SERVER_ERROR, "internal-error", "Unable to submit a review")));
        }
    }

    private static Optional<DateTime> getConversationCreationTime(List<com.gumtree.web.common.domain.messagecentre.Message> messages) {
        String creationTime = messages.get(0).getTime();
        try {
            return Optional.of(DateTime.parse(creationTime));
        } catch(Exception ex) {
            LOGGER.error("Error parsing conversation creation time: {}. Reason: {}", creationTime, ExceptionUtils.toShortMessage(ex));
            return Optional.empty();
        }
    }

    private static WebApiError convertErrorItemToWebApiError(ErrorItem errItem) {
        return new WebApiError(errItem.getCode(), errItem.getMessage(), errItem.getField());
    }
}
