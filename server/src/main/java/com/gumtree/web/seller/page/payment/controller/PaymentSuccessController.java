package com.gumtree.web.seller.page.payment.controller;

import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.gumtree.api.Account;
import com.gumtree.api.Ad;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.ApiOrderItem;
import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.api.domain.order.CreateOrderItemBean;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.common.domain.order.converter.ApiOrderToOrderEntityConverter;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.appbanner.AppBannerCookie;
import com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieHelper;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.PageContextSellerController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.manageads.api.CreateOrderApiCall;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.messages.OrderItemMessageGenerator;
import com.gumtree.web.seller.page.payment.model.PostAdErrorViewModel;
import com.gumtree.web.seller.page.payment.model.PostAdSuccessViewModel;
import com.gumtree.web.seller.page.payment.services.PaymentSuccessService;
import com.gumtree.web.seller.page.payment.util.PaymentSuccessUrl;
import com.gumtree.web.seller.page.postad.model.PostAdFeatureBean;
import com.gumtree.web.seller.page.postad.model.features.FeatureBean;
import com.gumtree.web.seller.page.postad.model.features.FeatureProductBuilder;
import com.gumtree.web.seller.page.postad.model.products.FeatureOption;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.reporting.ResponsiveGoogleAnalyticsConfigurer;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.gumtree.web.seller.service.CheckoutMetaInjector;
import com.gumtree.web.seller.service.convertr.ConvertrService;
import com.gumtree.web.zeno.checkout.CheckoutInput;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.checkout.CheckoutSuccessEvent;
import com.gumtree.zeno.core.event.user.sellerside.postad.AbstractPostAdEvent;
import com.gumtree.zeno.core.service.ZenoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.text.DecimalFormat;
import java.util.List;

import static com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieHelper.Action.POST_AD_COMPLETE;

/**
 * New responsive payment success message controller for responsive manage ads. This controller only implements the
 * tracking and advertising that are necessary for the payment confirmation page.
 */
@Controller
@GumtreePage(PageType.OrderSuccess)
@GoogleAnalytics(configurer = ResponsiveGoogleAnalyticsConfigurer.class, contextConfigured = true)
class PaymentSuccessController extends PageContextSellerController<Ad> {
  private static final Page PAGE = Page.PaymentSuccess;

  private final UserSession authenticatedUserSession;
  private final CheckoutContainer checkoutContainer;
  private final SuccessfulCheckoutManager successfulCheckoutManager;
  private final BushfireApi bushfireApi;
  private FeatureProductBuilder featureProductBuilder;
  private ApiOrderToOrderEntityConverter orderEntityConverter;
  private ZenoService zenoService;
  private AppBannerCookieHelper appBannerCookieHelper;
  private OrderItemMessageGenerator orderItemMessageGenerator;
  private CheckoutMetaInjector checkoutMetaInjector;
  private ConvertrService convertrService;
  private final PaymentSuccessService paymentSuccessService;

  @Autowired
  PaymentSuccessController(CookieResolver cookieResolver, CategoryModel categoryModel,
                           UserSession authenticatedUserSession, CheckoutContainer checkoutContainer,
                           ApiCallExecutor apiCallExecutor,
                           ErrorMessageResolver messageResolver,
                           UrlScheme urlScheme, SuccessfulCheckoutManager successfulCheckoutManager,
                           GumtreePageContext<Ad> pageContext, CategoryService categoryService,
                           LocationService locationService, BushfireApi bushfireApi,
                           FeatureProductBuilder featureProductBuilder,
                           ApiOrderToOrderEntityConverter orderEntityConverter, ZenoService zenoService,
                           UserSessionService userSessionService,
                           AppBannerCookieHelper appBannerCookieHelper,
                           CheckoutMetaInjector checkoutMetaInjector,
                           ConvertrService convertrService,
                           PaymentSuccessService paymentSuccessService) {
    super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, pageContext, categoryService,
        locationService, userSessionService);
    this.authenticatedUserSession = authenticatedUserSession;
    this.checkoutContainer = checkoutContainer;
    this.successfulCheckoutManager = successfulCheckoutManager;
    this.bushfireApi = bushfireApi;
    this.featureProductBuilder = featureProductBuilder;
    this.orderEntityConverter = orderEntityConverter;
    this.zenoService = zenoService;
    this.appBannerCookieHelper = appBannerCookieHelper;
    this.orderItemMessageGenerator = new OrderItemMessageGenerator(bushfireApi, categoryService,
        this::resolveMessage);
    this.checkoutMetaInjector = checkoutMetaInjector;
    this.convertrService = convertrService;
    this.paymentSuccessService = paymentSuccessService;
  }

  @RequestMapping(PaymentSuccessUrl.MAPPING)
  public ModelAndView showBookingSuccessPage(
      @PathVariable(PaymentSuccessUrl.CHECKOUT_KEY_PATH_VAR) String checkoutKey,
      HttpServletRequest request) {

    Account account = bushfireApi.accountApi().getAccount(authenticatedUserSession.getSelectedAccountId());
    Checkout checkout = checkoutContainer.getCheckout(checkoutKey);
    ApiOrder apiOrder = successfulCheckoutManager.processPayment(checkout.getOrder());

    if (!successfulCheckoutManager.paymentConfirmed(apiOrder)) {
      zenoService.logEvent(new CheckoutInput(apiOrder, checkout, "redirect"),
          PaymentCheckoutController.PAGE_PATH, CheckoutSuccessEvent.class);

      return new ModelAndView(new RedirectView(PaymentCheckoutController.PAGE_PATH));
    }

    successfulCheckoutManager.finishCheckout(checkout);

    boolean isNewUser = authenticatedUserSession.getUserType().isNew();

    Long advertId = getAdvertId(checkout, apiOrder);
    Ad ad = bushfireApi.advertApi().getAdvert(advertId);
    Order order = orderEntityConverter.convert(checkout.getOrder());

    PricingMetadata pricingMetadata = featureProductBuilder.populateFeatureMap(order, ad);
    // we only show upsell if we should show (is create or edit) AND the user has no features
    boolean showUpsell = !hasFeature(pricingMetadata) && checkout.isCreateOrEdit();

    Optional<Category> adCategory = categoryService.getById(ad.getCategoryId());
    Category category = adCategory.isPresent() ? adCategory.get() : getPageContext().getCategory();
    List<Category> categories = categoryService.getCategoriesList(category);

    String lvl1 = categories.get(1).getSeoName();
    String lvl2 = findCategory(categories, 2, "NULL");

    User user = authenticatedUserSession.getUser();
    AppBannerCookie appBannerCookie = appBannerCookieHelper.updateAppBannerCookie(POST_AD_COMPLETE, request);
    java.util.Optional<String> convertrUrl = convertrService.buildConvertrUrl(account, user, order, ad, category);

    CoreModel.Builder coreBuilder = getCoreModelBuilder(request, category).withAppBannerCookie(appBannerCookie)
        .withUserType(authenticatedUserSession.isProUser());
    PostAdSuccessViewModel.Builder viewModelBuilder = PostAdSuccessViewModel.builder(coreBuilder, PAGE)
        .withPostAdConfirmLink(paymentSuccessService.getPaymentSuccessUrl(checkout))
        .withInvoiceMail(user.getEmail())
        .withPaymentAmount(new DecimalFormat("#,##0.00").format((double) (apiOrder.getTotalIncVat()) / 100d))
        .withOrderReference(String.valueOf(apiOrder.getId()))
        .withAdvertUpdateNotices(generateUpdateNotices(apiOrder, account, ad, checkout.isCreateOrEdit()))
        .withUpsellAdvertId(String.valueOf(advertId))
        .withShowUpsell(showUpsell)
        .withConvertrUrl(convertrUrl.orElse(null))
        .withPricingMetadata(pricingMetadata)
        .withIsNewUser(isNewUser)
        .withHasAdvertImage(ad.getImages().size() > 0)
        .withL3Category(findCategory(categories, 3, ""))
        .withL4Category(findCategory(categories, 4, ""))
        .withAdCategories(lvl1 + "/" + lvl2)
        .withPostAdFeatureBean(new PostAdFeatureBean(advertId))
        .withAdvertTitle(ad.getTitle())
        .withViewUrl(getUrlScheme().urlFor(ad));

    getPageContext().setPageModel(ad);

    if (!orderContainsMultipleAdverts(apiOrder) && checkout.isCreateOrEdit()) {
      zenoService.logBackendEvent(containsNewAd(apiOrder), ad, AbstractPostAdEvent.class);
    }
    zenoService.logEvent(
        new CheckoutInput(apiOrder, checkout, checkout.isCreateOrEdit() ? "createOrEdit" : "feature"),
        PAGE.getTemplateName(), CheckoutSuccessEvent.class);

    return viewModelBuilder.build();
  }

  /**
   * Create order for features
   *
   * @param featureBean - feature details
   * @param advertId    - advert to apply features to
   * @param request     - http request
   * @return - next page to view
   */
  @RequestMapping(value = PaymentSuccessUrl.MAPPING, method = RequestMethod.POST)
  public final ModelAndView applyFeature(@ModelAttribute("postAdFeatureBean") PostAdFeatureBean featureBean,
                                         @ModelAttribute("advertId") String advertId,
                                         HttpServletRequest request) {

    CreateOrderBean createOrderBean = new CreateOrderBean();
    createOrderBean.setAccountId(authenticatedUserSession.getSelectedAccountId());
    createOrderBean.setItems(com.google.common.collect.Lists.newArrayList());

    for (ProductType productType : featureBean.getFeatures().keySet()) {
      FeatureBean feature = featureBean.getFeatures().get(productType);
      if (feature.getSelected() != null && feature.getSelected()) {
        CreateOrderItemBean createOrderItemBean = new CreateOrderItemBean();
        createOrderItemBean.setAdvertId(Long.parseLong(advertId));
        createOrderItemBean.setProductName(ProductName.valueOf(feature.getProductName()));
        createOrderBean.getItems().add(createOrderItemBean);
      }
    }
    ApiCallResponse<ApiOrder> response = execute(
        new CreateOrderApiCall(createOrderBean, authenticatedUserSession));

    if (response.isErrorResponse()) {
      return new PostAdErrorViewModel.Builder(getCoreModelBuilder(request))
          .withErrorCode(response.getErrorCode().asString())
          .build();
    }

    Ad ad = bushfireApi.advertApi().getAdvert(Long.valueOf(advertId));


    Checkout checkout = checkoutContainer
        .createCheckout(response.getResponseObject(), ad, false);

    Checkout checkoutFinal = checkoutMetaInjector.injectTrackingForManageAdsUpdate(checkout, false, ad);

    return redirect(PaymentCheckoutController.checkoutFormAction(checkoutFinal.getKey()));
  }

  private Long getAdvertId(Checkout checkout, ApiOrder apiOrder) {
    Long advertId;
    if (checkout.getAdvert() != null && checkout.getAdvert().getId() != null) {
      advertId = checkout.getAdvert().getId();
    } else if (checkout.getOrder() != null
        && checkout.getOrder().getItems() != null
        && !checkout.getOrder().getItems().isEmpty()) {

      advertId = checkout.getOrder().getItems().get(0).getAdvertId();
    } else {
      advertId = apiOrder.getItems().get(0).getAdvertId();
    }

    return advertId;
  }

  private boolean hasFeature(PricingMetadata pricingMetadata) {
    boolean hasFeature = pricingMetadata == null;
    if (!hasFeature) {
      for (FeatureOption option : pricingMetadata.getFeatureOptions().values()) {
        hasFeature = hasFeature || option.isActive();
      }
    }
    return hasFeature;
  }

  private String findCategory(List<Category> categories, int index, String alt) {
    return categories.size() > index ? categories.get(index).getSeoName() : alt;
  }

  private List<String> generateUpdateNotices(ApiOrder apiOrder, Account account, Ad ad, boolean createOrEdit) {
    List<String> updateNotices = Lists.newArrayList();

    noticesForTransactionDetails(apiOrder, ad, createOrEdit, updateNotices);
    updateNotices.addAll(orderItemMessageGenerator.noticesForOrderItems(apiOrder, account));

    return updateNotices;
  }

  private void noticesForTransactionDetails(ApiOrder apiOrder, Ad ad, boolean createOrEdit,
                                            List<String> updateNotices) {
    Long advertId = ad.getId();
    Runnable messageSuffixAppender = () -> {
    };
    if (orderContainsMultipleAdverts(apiOrder)) {
      updateNotices.add(resolveMessage("postad.ads.updated"));
    } else {
      if (createOrEdit) {
        if (containsNewAd(apiOrder)) {
          updateNotices.add(
              resolveMessage("postad.ad.posted", ad.getTitle(), advertId));
          updateNotices.add(
              resolveMessage("postad.moderation", getUrlScheme().urlFor(ad)));
          messageSuffixAppender = () -> updateNotices.add(
              resolveMessage("postad.policy.faq"));
        } else {
          updateNotices.add(
              resolveMessage("postad.ad.edited", ad.getTitle(), advertId, getUrlScheme().urlFor(ad)));
        }
      } else {
        updateNotices.add(resolveMessage("postad.ad.updated"));
      }
    }

    if (apiOrder.getId() == null && containsNewAd(apiOrder)) {
      updateNotices.add(resolveMessage("postad.free.email.sent", authenticatedUserSession.getUsername()));
    } else if (paymentMade(apiOrder)) {
      updateNotices.add(resolveMessage("postad.payment.email.sent", apiOrder.getId(),
          authenticatedUserSession.getUsername()));
    } else if (packagePaymentMade(apiOrder)) {
      updateNotices.add(resolveMessage("postad.package.payment.email.sent",
          apiOrder.getId(), authenticatedUserSession.getUsername()));
    }
    messageSuffixAppender.run();
  }

  private boolean orderContainsMultipleAdverts(ApiOrder order) {
    return (Sets.newHashSet(Collections2.transform(
        order.getItems(),
        advertIdsInOrder()))).size() > 1;
  }

  private Function<ApiOrderItem, Long> advertIdsInOrder() {
    return new Function<ApiOrderItem, Long>() {
      @Override
      public Long apply(ApiOrderItem apiOrderItem) {
        return apiOrderItem.getAdvertId();
      }
    };
  }

  private boolean containsNewAd(ApiOrder order) {
    for (ApiOrderItem item : order.getItems()) {
      if (item.getProductName().equals(ProductName.INSERTION)) {
        return true;
      }
    }
    return false;
  }

  private boolean paymentMade(ApiOrder order) {
    return order.getTotalIncVat() != 0L;
  }

  private boolean packagePaymentMade(ApiOrder order) {
    for (ApiOrderItem item : order.getItems()) {
      if (item.getPaymentDetail() != null && item.getPaymentDetail().getCreditPackage() != null) {
        return true;
      }
    }
    return false;
  }
}
