package com.gumtree.web.seller.page.password.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.password.model.ForgottenPasswordConfirmationModel;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.zeno.core.domain.PageType;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

import java.util.Map;

import static com.gumtree.web.seller.page.password.controller.ForgottenPasswordEmailConfirmationController.PAGE_PATH;

@Controller
@RequestMapping(PAGE_PATH)
@GoogleAnalytics
@GumtreePage(PageType.PasswordResetSent)
public final class ForgottenPasswordEmailConfirmationController extends BaseSellerController {

    public static final String PAGE_PATH = "/forgotten-password-confirmation";

    private final ParameterEncryption parameterEncryption;

    @Autowired
    public ForgottenPasswordEmailConfirmationController(CookieResolver cookieResolver,
                                                        CategoryModel categoryModel,
                                                        ApiCallExecutor apiCallExecutor,
                                                        ErrorMessageResolver messageResolver,
                                                        UrlScheme urlScheme,
                                                        UserSessionService userSessionService,
                                                        ParameterEncryption parameterEncryption) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.parameterEncryption = parameterEncryption;
    }

    @RequestMapping(method = RequestMethod.GET)
    public ModelAndView showPage(HttpServletRequest request) {

        String encrypted = request.getParameter(ForgottenPasswordController.ENCRYPTED_MAP_PARAMETER_NAME);
        if(StringUtils.isNotBlank(encrypted)) {
            Map<String, String> data = parameterEncryption.decryptUrlEncodedParameterMap(encrypted);
            String emailAddress = data.get(ForgottenPasswordController.EMAIL_FLASH_ATTRIBUTES);
            CoreModel.Builder coreModel = getCoreModelBuilder(request);
            ForgottenPasswordConfirmationModel.Builder resetPasswordConfirmationModel = ForgottenPasswordConfirmationModel.builder()
                    .withEmailAddress(emailAddress);
            return resetPasswordConfirmationModel.build(coreModel);
        } else {
            return redirect(ForgottenPasswordController.PAGE_PATH);
        }

    }
}
