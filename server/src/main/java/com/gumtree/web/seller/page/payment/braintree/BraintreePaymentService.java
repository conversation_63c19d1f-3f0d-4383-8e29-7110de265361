package com.gumtree.web.seller.page.payment.braintree;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.api.PaymentApiClient;
import com.gumtree.web.seller.page.payment.controller.PaymentCheckoutController;
import com.gumtree.web.seller.page.payment.event.PaymentEventsListener;
import com.gumtree.web.seller.page.payment.model.BillingAddress;
import com.gumtree.web.seller.page.payment.model.PaymentKeys;
import com.gumtree.web.seller.page.payment.services.PaymentSuccessService;
import com.gumtree.web.seller.service.payment.OrderAlreadyPaidException;
import com.gumtree.web.seller.service.payment.PaymentDeclinedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class BraintreePaymentService implements PaymentService {
  private static final String PAYPAL_INSTRUMENT_TYPE = "paypal_account";
  private static final ObjectMapper JSON_MAPPER = new ObjectMapper();
  private final PaymentApiClient paymentApiClient;
  private final BushfireApi bushfireApi;
  private final PaymentEventsListener paymentEvents;
  private final PaymentSuccessService paymentSuccessService;

  @Autowired
  public BraintreePaymentService(
      PaymentApiClient paymentApiClient,
      BushfireApi bushfireApi,
      PaymentEventsListener paymentEvents,
      PaymentSuccessService paymentSuccessService) {
    this.paymentApiClient = paymentApiClient;
    this.bushfireApi = bushfireApi;
    this.paymentEvents = paymentEvents;
    this.paymentSuccessService = paymentSuccessService;
  }

  @Override
  public PaymentKeys getPaymentKeys(Long orderId) {
    return paymentApiClient.intialisePayment(orderId);
  }

  @Override
  public String paymentTransaction(
      Checkout checkout, String paymentMethodNonce, User user, String deviceData,
      Long accountId,
      BillingAddress billingAddress, String platformDevice) {

    if (!Strings.isNullOrEmpty(paymentMethodNonce)) {
      try {
        paymentApiClient.executePayment(checkout, user, accountId, billingAddress, paymentMethodNonce, deviceData, platformDevice);
        return paymentSuccessful(checkout);
      } catch (OrderAlreadyPaidException ex) {
        return orderAlreadyPaidException(checkout, ex);
      } catch (PaymentDeclinedException ex) {
        return braintreeTransactionFailed(checkout, ex);
      } catch (RuntimeException ex) {
        return unexpectedProblem(checkout, ex);
      }
    } else {
      return invalidPaymentMethod(checkout, paymentMethodNonce);
    }

  }

  @Override
  public PaymentKeys getPaymentKeys(Long orderId, Long userid) {
    return paymentApiClient.generateToken(orderId, userid);
  }

  private String paymentSuccessful(Checkout checkout) {
    paymentEvents.successfulPaymentForTransaction(checkout.getOrder().getId());
    return paymentSuccessfulJson(paymentSuccessService.getPaymentSuccessUrl(checkout));
  }


  private String invalidPaymentMethod(Checkout checkout, String paymentMethod) {
    paymentEvents.invalidPaymentMethodProblem(paymentMethod, checkout);
    return getErrorJsonResponse();
  }

  private String braintreeTransactionFailed(Checkout checkout, PaymentDeclinedException ex) {
    paymentEvents.failedPaymentWithMessage(ex.getMessage(), checkout);
    return getErrorJsonResponse();
  }

  private String unexpectedProblem(Checkout checkout, Exception e) {
    paymentEvents.unexpectedPaymentProblemForOrder(getOrderId(checkout), e, checkout);
    return getErrorJsonResponse();
  }

  private String getErrorJsonResponse() {
    Map<String, String> response = Maps.newHashMap();
    response.put("errorMsg", PaymentCheckoutController.PAYMENT_ERROR_MESSAGE);
    return urlOrErrorJson(response);
  }

  private String orderAlreadyPaidException(Checkout checkout, OrderAlreadyPaidException ex) {
    paymentEvents.alreadyPaidOrder(ex.getOrderId());
    return paymentSuccessfulJson(paymentSuccessService.getPaymentSuccessUrl(checkout));
  }

  private String paymentSuccessfulJson(String url) {
    Map<String, String> response = Maps.newHashMap();
    response.put("url", url);
    return urlOrErrorJson(response);
  }

  private String urlOrErrorJson(Map<String, String> map) {
    try {
      return JSON_MAPPER.writeValueAsString(map);
    } catch (JsonProcessingException e) {
      return "";
    }
  }

  private static Long getOrderId(Checkout checkout) {
    return checkout.getOrder().getId();
  }
}
