package com.gumtree.web.seller.page.manageads.metric;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CustomMetricRegistry {
    private MeterRegistry meterRegistry;

    @Autowired
    public CustomMetricRegistry(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    public void metricCounter(String name) {
        Counter.builder(name)
                .register(meterRegistry)
                .increment();
    }

    public void metricCounter(String name, String productName) {
        Counter.builder(name)
                .tag("criteria", productName)
                .register(meterRegistry)
                .increment();
    }

    public Timer madPageTimer(String phase) {
        return Timer.builder("mad_page")
                .tag("phase", phase)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }

    public Timer myAccountPageTimer(String phase) {
        return Timer.builder("my_account_page")
                .tag("phase", phase)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }

    public Timer postAdStatePageTimer(String phase) {
        return Timer.builder("post_ad_state_page")
                .tag("phase", phase)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }

    public Timer postAdSubmitPageTimer(String phase) {
        return Timer.builder("post_ad_submit_page")
                .tag("phase", phase)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }

    public Timer checkoutPageTimer(String phase) {
        return Timer.builder("checkout_page")
                .tag("phase", phase)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }

    public Timer bumpUpPageTimer(String phase) {
        return Timer.builder("bump_up_page")
                .tag("phase", phase)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }

    public Timer bumpUpPageForPhoneVerificationTimer(String phase) {
        return Timer.builder("bump_up_page_for_phone_verification")
                .tag("phase", phase)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }

    public Timer syncPhoneVerifyStateTimer(String phase) {
        return Timer.builder("sync_phone_verify_state")
                .tag("phase", phase)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }

    public Timer workspaceTimer(String phase) {
        return Timer.builder("workspace")
                .tag("phase", phase)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }
    public Timer editorTimer(String phase) {
        return Timer.builder("editor")
                .tag("phase", phase)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }

    public Timer AIResponseTimer(String endpoint) {
        return Timer.builder("AIResponse")
                .tag("phase", endpoint)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }

    public Counter counter(String name, String tag, String value) {
        return meterRegistry.counter(name, tag, value);
    }
}
