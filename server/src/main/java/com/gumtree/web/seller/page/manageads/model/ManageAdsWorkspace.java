package com.gumtree.web.seller.page.manageads.model;

import java.io.Serializable;

/**
 * Manage Ads session bean
 */
public interface ManageAdsWorkspace extends Serializable {
    /**
     * @return filter form bean
     */
    ManageAdsFilterFormBean getFilterForm();

    /**
     * Filters the manage ads form.
     *
     * @param manageAdsFilterFormBean - filter detail
     */
    void setFilterForm(ManageAdsFilterFormBean manageAdsFilterFormBean);

    /**
     * Removes a manage ads filter.
     */
    void removeFilterForm();
}
