package com.gumtree.web.seller.page.manageads.model;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.gumtree.api.AdStatus;
import com.gumtree.web.seller.page.common.SelectableValue;

import java.util.List;
import java.util.Set;

/**
 * Manage ads status filter enumeration
 */
public enum ManageAdStatus implements SelectableValue {

    ACTIVE_ADS("Active Ads", Sets.newHashSet(
            AdStatus.LIVE,
            AdStatus.AWAITING_PHONE_VERIFIED,
            AdStatus.DRAFT,
            AdStatus.AWAITING_ACTIVATION,
            AdStatus.NEEDS_EDITING,
            AdStatus.AWAITING_SCREENING,
            AdStatus.AWAITING_CS_REVIEW)),
    LIVE("Live", Sets.newHashSet(AdStatus.LIVE)),
    AWAITING_PHONE_VERIFIED("Awaiting phone verification", Sets.newHashSet(AdStatus.AWAITING_PHONE_VERIFIED)),
    DRAFT("Draft", Sets.newHashSet(AdStatus.DRAFT)),
    AWAITING_ACTIVATION("Awaiting activation", Sets.newHashSet(AdStatus.AWAITING_ACTIVATION)),
    NEEDS_EDITING("Needs editing", Sets.newHashSet(AdStatus.NEEDS_EDITING)),
    PROCESSING("Processing", Sets.newHashSet(
            AdStatus.AWAITING_SCREENING,
            AdStatus.AWAITING_CS_REVIEW)),
    INACTIVE_ADS("Inactive Ads", Sets.newHashSet(
            AdStatus.DELETED_CS,
            AdStatus.DELETED_USER,
            AdStatus.EXPIRED)),
    ACTIVE_AND_INACTIVE_ADS("Active and Inactive Ads",
            Sets.union(ACTIVE_ADS.getStatuses(), INACTIVE_ADS.getStatuses())
    ),

    DELETED("Deleted", Sets.newHashSet(AdStatus.DELETED_USER)),
    EXPIRED("Expired", Sets.newHashSet(AdStatus.EXPIRED)),
    REMOVED("Removed", Sets.newHashSet(AdStatus.DELETED_CS));

    private final String description;

    private final Set<AdStatus> statuses;

    ManageAdStatus(String description, Set<AdStatus> statuses) {
        this.description = description;
        this.statuses = statuses;
    }



    public String getName() {
        return description;
    }

    public Set<AdStatus> getStatuses() {
        return statuses;
    }

    public List<String> getStatusesNames() {
        List<String> statusList = Lists.newArrayList(
                Collections2.transform(
                    getStatuses(),
                    new Function<AdStatus, String>() {
                        @Override
                        public String apply(AdStatus v) {
                            return v.name();
                        }
                    }
                ));
        return statusList;
    }

    /**
     *
     * @param adStatus - the ad status
     * @return the Enum value of the ad status
     */
    public static ManageAdStatus valueOf(AdStatus adStatus) {
        switch(adStatus) {
            case LIVE: return LIVE;
            case AWAITING_PHONE_VERIFIED: return AWAITING_PHONE_VERIFIED;
            case DRAFT: return DRAFT;
            case AWAITING_ACTIVATION: return AWAITING_ACTIVATION;
            case NEEDS_EDITING: return NEEDS_EDITING;
            case AWAITING_SCREENING:
            case AWAITING_CS_REVIEW: return PROCESSING;
            case DELETED_USER: return DELETED;
            case DELETED_CS: return REMOVED;
            case EXPIRED: return EXPIRED;
            default:
                throw new RuntimeException("unknown value for ad status");
        }
    }

    @Override
    public String getDisplayValue() {
        return description;
    }

    @Override
    public String getValue() {
        return name();
    }
}
