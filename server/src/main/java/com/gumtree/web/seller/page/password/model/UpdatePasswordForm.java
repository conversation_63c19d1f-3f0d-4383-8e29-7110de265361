package com.gumtree.web.seller.page.password.model;

import com.google.common.collect.Maps;
import com.gumtree.api.domain.user.beans.UpdatePasswordBean;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;

import java.util.List;
import java.util.Map;

/**
 * As we are using a bean in BAPI we can't really use the Form abstract class
 * as in other forms. But we need to follow the same pattern due to FE standards on
 * templates
 * <p/>
 * The only solution, given the lack of traits and we not using Java 8 yet, is to
 * just duplicate some code in here (the FormErrors part). A minor sin, but no other
 * simple option I cna think of
 */
public class UpdatePasswordForm extends UpdatePasswordBean {

    private Map<String, List<String>> formErrors = Maps.newHashMap();

    public void addErrors(ReportableErrorsMessageResolvingErrorSource errors) {
        formErrors.putAll(errors.getAllResolvedFieldErrorMessages());
        formErrors.put("global", errors.getResolvedGlobalErrorMessages());
    }

    public Map<String, List<String>> getFormErrors() {
        return formErrors;
    }
}

