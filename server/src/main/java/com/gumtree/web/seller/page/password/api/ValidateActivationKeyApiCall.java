package com.gumtree.web.seller.page.password.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.client.executor.ApiCall;

/**
 * Implementation of {@link com.gumtree.api.client.executor.ApiCall} to validate an activation key/ username combination.
 */
public final class ValidateActivationKeyApiCall implements ApiCall<Void> {

    private String username;

    private String activationKey;

    /**
     * Constructor.
     *
     * @param username      the username to activate
     * @param activationKey the activation key that was supplied by the user
     */
    public ValidateActivationKeyApiCall(String username, String activationKey) {
        this.username = username;
        this.activationKey = activationKey;
    }

    @Override
    public Void execute(BushfireApi api) {
        api.create(UserApi.class).validateActivationKey(username, activationKey);
        return null;
    }
}
