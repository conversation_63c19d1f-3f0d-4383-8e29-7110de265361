package com.gumtree.web.seller.page.payment.util;

import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Arrays;
import java.util.List;

/**
 * URL object for generating urls for successful payment
 */
public class PaymentSuccessUrl {

  public static final String CHECKOUT_KEY_PATH_VAR = "checkoutKey";
  public static final String MAPPING = "/thankyou/{" + CHECKOUT_KEY_PATH_VAR + "}";
  private static final String EMPTY_STRING = "";
  private static final String NONE = "none";
  private static final String URL_BASE = "/thankyou/";
  private static final String DELIMETER = ",";

  private final String url;

  private List<ProductType> featureNames = Arrays.asList(ProductType.BUMP_UP,
      ProductType.URGENT,
      ProductType.FEATURED,
      ProductType.SPOTLIGHT);

  @Deprecated
  public PaymentSuccessUrl(Checkout checkout) {
    String url;
    try {
      url = UriComponentsBuilder.fromUriString(getBaseURL(checkout))
          .queryParam("action", checkout.getMetaPathInfo().getPageActionType())
          .queryParam("payment", makeString(checkout.getMetaPathInfo().getPagePaymentTypes()))
          .queryParam("type", makeString(checkout.getMetaPathInfo().getFeatureTypes()))
          .queryParam("multiple", checkout.getMetaPathInfo().getIsMultipleAds() ? "yes" : "no")
          .build().toUri().toString();

    } catch (Exception e) {
      url = UriComponentsBuilder.fromUriString(getBaseURL(checkout)).build().toUri().toString();
    }
    this.url = url;
  }

  private String getBaseURL(Checkout checkout) {
    return URL_BASE + checkout.getKey();
  }

  private String makeString(List<? extends Object> items) {
    if ((items == null) || items.isEmpty()) {
      return NONE;
    }

    String foldedItems = items.stream().map(x -> x.toString())
        .reduce(EMPTY_STRING, (a, b) -> a + b + DELIMETER);

    return foldedItems.endsWith(DELIMETER) ? foldedItems.substring(0, foldedItems.length() - 1) : foldedItems;
  }

  public String get() {
    return this.url;
  }
}
