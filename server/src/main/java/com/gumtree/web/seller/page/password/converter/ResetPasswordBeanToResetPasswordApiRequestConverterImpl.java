package com.gumtree.web.seller.page.password.converter;

import com.gumtree.api.client.request.EmailRequest;
import com.gumtree.web.seller.page.password.controller.ResetPasswordFormBean;
import org.springframework.stereotype.Component;

/**
 * Converter for converting password reset beans.
 */
@Component
public class ResetPasswordBeanToResetPasswordApiRequestConverterImpl
        implements ResetPasswordBeanToResetPasswordApiRequestConverter {

    @Override
    public final EmailRequest convert(ResetPasswordFormBean bean) {
        EmailRequest request = new EmailRequest();
        request.setEmail(bean.getUsername());
        return request;
    }
}
