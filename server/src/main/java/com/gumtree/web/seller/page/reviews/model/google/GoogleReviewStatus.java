package com.gumtree.web.seller.page.reviews.model.google;

/**
 * <AUTHOR>
 */
public enum GoogleReviewStatus {

    NO_AUTHORIZATION(1, "NO_AUTHORIZATION"),

    DATA_SYNCING(2, "HAS_AUTHORIZATION AND DATA_SYNCING"),

    NO_MERCHANT_INFO(3, "HAS_AUTHORIZATION AND DATA_NOT_SYNCING AND NO_MERCHANT_INFO"),

    OK(4, "NORMAL"),

    DO_NOT_NEED_SHOW(5, "DO_NOT_NEED_SHOW");

    private final int value;
    private final String description;

    GoogleReviewStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public String getName() {
        return this.name();
    }
} 