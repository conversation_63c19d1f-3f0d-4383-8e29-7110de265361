package com.gumtree.web.seller.page.payment.api;

import com.gumtree.api.User;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.seller.infrastructure.driven.payment.braintree.BraintreeApi;
import com.gumtree.seller.infrastructure.driven.payment.braintree.model.*;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.model.BillingAddress;
import com.gumtree.web.seller.page.payment.model.PaymentKeys;

import static java.lang.String.format;


public class PaymentApiClient {

    private static final String CLIENT_ID = "seller";

    private BraintreeApi braintreeApi;

    public PaymentApiClient(BraintreeApi braintreeApi) {
        this.braintreeApi = braintreeApi;
    }

    public PaymentKeys intialisePayment(Long orderId) {

        try {
            TransactionInitialisedResponse response = braintreeApi.initialiseTransaction(orderId, CLIENT_ID)
                    .toObservable().toBlocking().first();
            return new PaymentKeys(response.getMerchantId(), response.getToken(), response.getEnvironmentName());
        } catch (Exception apiException) {
            throw new PaymentApiException(format("braintreeApi.intialisePayment call failed for orderId:%d", orderId), apiException);
        }
    }

    public PaymentKeys generateToken(Long orderId,Long userId) {

        try {
            GenerateTokenRequest generateTokenRequest = new GenerateTokenRequest();
            generateTokenRequest.setOrderId(orderId);
            generateTokenRequest.setCustomerId(userId);
            TransactionInitialisedResponse response = braintreeApi.generateToken(generateTokenRequest, CLIENT_ID)
                    .toObservable().toBlocking().first();
            return new PaymentKeys(response.getMerchantId(), response.getToken(), response.getEnvironmentName());
        } catch (Exception apiException) {
            throw new PaymentApiException(format("braintreeApi.intialisePayment call failed for orderId:%d", orderId), apiException);
        }
    }

    public void executePayment(Checkout checkout, User user, Long accountId,
                               BillingAddress billingAddress, String paymentMethodNonce, String deviceData, String platformDevice) {

        ApiOrder order = checkout.getOrder();
        Long orderId = order.getId();
        try {
            ExecuteTransactionRequest executeTransactionRequest = new ExecuteTransactionRequest()
                    .accountId(accountId)
                    .userId(user.getId())
                    .email(user.getEmail())
                    .billingAddress(new com.gumtree.seller.infrastructure.driven.payment.braintree.model.BillingAddress()
                            .firstName(billingAddress.getFirstName().orElse(null))
                            .lastName(billingAddress.getLastName().orElse(null))
                            .street(billingAddress.getAddress().orElse(null))
                            .town(billingAddress.getTownCity().orElse(null))
                            .country(billingAddress.getCountry().orElse(null))
                            .postcode(billingAddress.getPostcode().orElse(null))
                    )
                    .paymentDetails(new PaymentDetails()
                            .totalIncludingVat(order.getTotalIncVat())
                            .totalVat(order.getTotalVat())
                            .paymentMethodNonce(paymentMethodNonce)
                            .deviceData(deviceData)
                    )
                    .platformDevice(platformDevice);

            braintreeApi.executeTransaction(orderId, executeTransactionRequest, CLIENT_ID);
        } catch (Exception apiException) {
            throw new PaymentApiException(
                                format("braintreeApi.executePayment call failed for orderId:%s", orderId), apiException);
        }
    }

    public static class PaymentApiException extends RuntimeException {
        public PaymentApiException(String message, Throwable t) {
            super(message, t);
        }
    }
}


