package com.gumtree.web.seller.page.reviews.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

public class ReviewCommonResponse<T> {
    private Integer code;
    private String msg;
    private T data;

    public static <T> ReviewCommonResponse<T> success(T data) {
        ReviewCommonResponse<T> obj = new ReviewCommonResponse<>();
        obj.setCode(0);
        obj.setData(data);
        return obj;
    }

    public static <T> ReviewCommonResponse<T> fail(String msg) {
        ReviewCommonResponse<T> obj = new ReviewCommonResponse<>();
        obj.setCode(-1);
        obj.setMsg(msg);
        return obj;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
