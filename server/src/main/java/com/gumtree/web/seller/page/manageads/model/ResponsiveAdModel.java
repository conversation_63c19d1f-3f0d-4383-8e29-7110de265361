package com.gumtree.web.seller.page.manageads.model;

import java.io.Serializable;
import java.util.Map;

public class ResponsiveAdModel implements Serializable {

    private String adId;
    private String age;
    private String views;
    private String listingViews;
    private String replies;
    private String bumpTimes;
    private String rootImgUrl;
    private String advertUrl;
    private String editAdvertUrl;
    private String altTag;
    private String status;
    private String displayLocationText;
    private String displayPrice;
    private String title;
    private String lastModifiedTime;
    private String postedTime;
    private String sellerType;

    private String bumpPrice;
    private String urgentPrice;
    private String topAd3Price;
    private String topAd7Price;
    private String topAd14Price;
    private String spotlightPrice;
    private Long l1CategoryId;
    private Long l2CategoryId;
    private Long l3CategoryId;

    private Map<String, Boolean> matrix;
    private Map<String, String> expires;

    private boolean isUrgent;
    private boolean isFeatured;
    // GTCF-136 we set default values as true as it is the most common state
    private boolean isDeletable = true;
    private boolean isEditable = true;
    private boolean isFeaturable = true;
    private boolean canBeUrgent = true;
    private boolean canBeHomepageSpotlight = true;
    private boolean canBeBumpedUp = true;
    private boolean isReadOnlyCategory = false;
    private boolean markedAsSold = false;
    private boolean autoRepostEnabled = true;

    public String getAdId() {
        return adId;
    }

    public void setAdId(String adId) {
        this.adId = adId;
    }

    public String getViews() {
        return views;
    }

    public void setViews(String views) {
        this.views = views;
    }

    public String getListingViews() {
        return listingViews;
    }

    public void setListingViews(String listingViews) {
        this.listingViews = listingViews;
    }

    public String getReplies() {
        return replies;
    }

    public void setReplies(String replies) {
        this.replies = replies;
    }

    public String getBumpTimes() {
        return bumpTimes;
    }

    public void setBumpTimes(String bumpTimes) {
        this.bumpTimes = bumpTimes;
    }

    public boolean isUrgent() {
        return isUrgent;
    }

    public void setUrgent(boolean urgent) {
        isUrgent = urgent;
    }

    public boolean isFeatured() {
        return isFeatured;
    }

    public void setFeatured(boolean featured) {
        isFeatured = featured;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getBumpPrice() {
        return bumpPrice;
    }

    public void setBumpPrice(String bumpPrice) {
        this.bumpPrice = bumpPrice;
    }

    public String getUrgentPrice() {
        return urgentPrice;
    }

    public void setUrgentPrice(String urgentPrice) {
        this.urgentPrice = urgentPrice;
    }

    public String getTopAd3Price() {
        return topAd3Price;
    }

    public void setTopAd3Price(String topAd3Price) {
        this.topAd3Price = topAd3Price;
    }

    public String getTopAd7Price() {
        return topAd7Price;
    }

    public void setTopAd7Price(String topAd7Price) {
        this.topAd7Price = topAd7Price;
    }

    public String getTopAd14Price() {
        return topAd14Price;
    }

    public void setTopAd14Price(String topAd14Price) {
        this.topAd14Price = topAd14Price;
    }

    public String getSpotlightPrice() {
        return spotlightPrice;
    }

    public void setSpotlightPrice(String spotlightPrice) {
        this.spotlightPrice = spotlightPrice;
    }

    public String getRootImgUrl() {
        return rootImgUrl;
    }

    public void setRootImgUrl(String rootImgUrl) {
        this.rootImgUrl = rootImgUrl;
    }

    public Map<String, Boolean> getMatrix() {
        return matrix;
    }

    public void setMatrix(Map<String, Boolean> matrix) {
        this.matrix = matrix;
    }

    public Map<String, String> getExpires() {
        return expires;
    }

    public void setExpires(Map<String, String> expires) {
        this.expires = expires;
    }

    public boolean isDeletable() {
        return isDeletable;
    }

    public void setDeletable(boolean deletable) {
        isDeletable = deletable;
    }

    public boolean isEditable() {
        return isEditable;
    }

    public void setEditable(boolean editable) {
        isEditable = editable;
    }

    public boolean isFeaturable() {
        return isFeaturable;
    }

    public void setFeaturable(boolean featurable) {
        isFeaturable = featurable;
    }

    public boolean isCanBeUrgent() {
        return canBeUrgent;
    }

    public void setCanBeUrgent(boolean canBeUrgent) {
        this.canBeUrgent = canBeUrgent;
    }

    public boolean isCanBeHomepageSpotlight() {
        return canBeHomepageSpotlight;
    }

    public void setCanBeHomepageSpotlight(boolean canBeHomepageSpotlight) {
        this.canBeHomepageSpotlight = canBeHomepageSpotlight;
    }

    public boolean isCanBeBumpedUp() {
        return canBeBumpedUp;
    }

    public void setCanBeBumpedUp(boolean canBeBumpedUp) {
        this.canBeBumpedUp = canBeBumpedUp;
    }

    public String getAdvertUrl() {
        return advertUrl;
    }

    public void setAdvertUrl(String advertUrl) {
        this.advertUrl = advertUrl;
    }

    public String getAltTag() {
        return altTag;
    }

    public void setAltTag(String altTag) {
        this.altTag = altTag;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDisplayLocationText() {
        return displayLocationText;
    }

    public void setDisplayLocationText(String displayLocationText) {
        this.displayLocationText = displayLocationText;
    }

    public String getDisplayPrice() {
        return displayPrice;
    }

    public void setDisplayPrice(String displayPrice) {
        this.displayPrice = displayPrice;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(String lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    public String getEditAdvertUrl() {
        return editAdvertUrl;
    }

    public void setEditAdvertUrl(String editAdvertUrl) {
        this.editAdvertUrl = editAdvertUrl;
    }

    public String getPostedTime() {
        return postedTime;
    }

    public void setPostedTime(String postedTime) {
        this.postedTime = postedTime;
    }

    public boolean isReadOnlyCategory() {
        return isReadOnlyCategory;
    }

    public void setReadOnlyCategory(boolean readOnlyCategory) {
        isReadOnlyCategory = readOnlyCategory;
    }

    public Long getL1CategoryId() {
        return l1CategoryId;
    }

    public void setL1CategoryId(Long l1CategoryId) {
        this.l1CategoryId = l1CategoryId;
    }

    public Long getL2CategoryId() {
        return l2CategoryId;
    }

    public void setL2CategoryId(Long l2CategoryId) {
        this.l2CategoryId = l2CategoryId;
    }

    public Long getL3CategoryId() {
        return l3CategoryId;
    }

    public void setL3CategoryId(Long l3CategoryId) {
        this.l3CategoryId = l3CategoryId;
    }

    public String getSellerType() {
        return sellerType;
    }

    public void setSellerType(String sellerType) {
        this.sellerType = sellerType;
    }

    public boolean isMarkedAsSold() {
        return markedAsSold;
    }

    public void setMarkedAsSold(boolean markedAsSold) {
        this.markedAsSold = markedAsSold;
    }

    public boolean isAutoRepostEnabled() {
        return autoRepostEnabled;
    }

    public void setAutoRepostEnabled(boolean autoRepostEnabled) {
        this.autoRepostEnabled = autoRepostEnabled;
    }
}