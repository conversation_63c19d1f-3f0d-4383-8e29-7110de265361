package com.gumtree.web.seller.page.manageads.model;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.GsonBuilder;
import com.gumtree.api.Account;
import com.gumtree.bapi.ListingCapApi;
import com.gumtree.bapi.model.ListingCapResponse;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.SimpleLink;
import com.gumtree.util.url.CdnImageUrlProviderImpl;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.seller.model.AdPreview;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.common.page.model.pagination.PaginationSection;
import com.gumtree.web.common.page.model.pagination.decadicpagination.DecadicPagination;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.manageads.ManageAdsController;
import com.gumtree.web.seller.page.manageads.ManageAdsHelper;
import com.gumtree.web.seller.page.postad.model.products.ProductPrice;
import com.gumtree.web.seller.service.adstats.AdvertStatisticData;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.ModelAndView;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;


public class ManageAdsModel extends CommonModel implements Serializable {

    private static final Logger LOGGER = LoggerFactory.getLogger(ManageAdsController.class);

    public static final String SELLER_TYPE_PRIVATE = "private";
    public static final List<String> INACTIVE_STATES = ManageAdStatus.INACTIVE_ADS.getStatusesNames();;

    private Map<String, ResponsiveAdModel> adverts = Maps.newLinkedHashMap();
    private List<ResponsiveAdModel> advertsList = Lists.newArrayList();

    private Boolean isProUser;
    private ManageAdsFeatureFormBean featureForm; // jsp
    private ManageAdsFilterFormBean filterForm; // ftl jsp
    private ManageAdsAccountSelectionFormBean accountSelectionForm; // jsp
    private Integer currentPageNumber; // ftl
    private Integer totalNumberOfPages; // ftl
    private List<PaginationSection> paginationSections; // jsp
    private DecadicPagination decadicPagination; // jsp
    private boolean showDeleteAdBalloon; // jsp
    private String search; // jsp
    private boolean emptySearch; // jsp
    private int advertCount; // ftl jsp
    private Date clock; // tag
    private Map<String, AdvertStatisticData> adViewStats; // tag
    private ManageAdStatus status; // jsp
    private Integer bumpupRestrictionMinutes; // tag
    private Map<Long, Map<String, ProductPrice>> prices; // tag
    private boolean enableMarketingOptIn; // ftl
    private boolean showManageJobsOnMadgexButton;
    private ListingCapApi listingCapApi;
    private String error; // jsp ftl
    private ReportableErrorsMessageResolvingErrorSource errors; // jsp
    private boolean googleReviewSwitch; // show google jump

    public ManageAdsModel(CoreModel core) {
        super(core);
    }

    public Boolean getIsProUser() {
        return isProUser;
    }

    public ManageAdsFeatureFormBean getFeatureForm() {
        return featureForm;
    }

    public ManageAdsFilterFormBean getFilterForm() {
        return filterForm;
    }

    public ManageAdsAccountSelectionFormBean getAccountSelectionForm() {
        return accountSelectionForm;
    }

    public Integer getCurrentPageNumber() {
        return currentPageNumber;
    }

    public Integer getTotalNumberOfPages() {
        return totalNumberOfPages;
    }

    public List<PaginationSection> getPaginationSections() {
        return paginationSections;
    }

    public DecadicPagination getDecadicPagination() {
        return decadicPagination;
    }

    public boolean isShowDeleteAdBalloon() {
        return showDeleteAdBalloon;
    }

    public int getAdvertCount() {
        return advertCount;
    }

    public String getSearch() {
        return search;
    }

    public boolean isEmptySearch() {
        return emptySearch;
    }

    public Map<Long, Map<String, ProductPrice>> getPrices() {
        return prices;
    }

    public Map<String, AdvertStatisticData> getAdViewStats() {
        return adViewStats;
    }

    public ManageAdStatus getStatus() {
        return status;
    }

    public Date getClock() {
        return clock;
    }

    public Integer getBumpupRestrictionMinutes() {
        return bumpupRestrictionMinutes;
    }

    public boolean isEnableMarketingOptIn() {
        return enableMarketingOptIn;
    }

    public boolean isShowManageJobsOnMadgexButton() {
        return showManageJobsOnMadgexButton;
    }

    public ListingCapApi getListingCapApi() {
        return listingCapApi;
    }

    public String getError() {
        return error;
    }

    public ReportableErrorsMessageResolvingErrorSource getErrors() {
        return errors;
    }

    public boolean isGoogleReviewSwitch() {
        return googleReviewSwitch;
    }

    public void setGoogleReviewSwitch(boolean googleReviewSwitch) {
        this.googleReviewSwitch = googleReviewSwitch;
    }

    public Map<String, ResponsiveAdModel> getAdverts() {
        return adverts;
    }

    public String getAdvertsJson() {
        return new GsonBuilder().create().toJson(advertsList);
    }

    public String getAction() {
        return ManageAdsController.PAGE_PATH + ManageAdsController.BUY_FEATURES;
    }

    public String getTopAdChecboxValue() {
        return ManageAdsController.BUY_TOP_AD;
    }

    private String getRootImgUrl(String url) {
        if (url != null && url.contains(CdnImageUrlProviderImpl.CLOUDFLARE_URL_PREFIX)) {
            return url;
        }
        if (url != null && url.length() > 6) {
            return url.substring(0, url.length() - 6);
        }
        return "";
    }

    private void populateAdData(Account account,
                                ListingCapApi listingCapApi,
                                ManageAdsFeatureFormBean manageAdsFeatureFormBean,
                                List<AdvertStatisticData> statisticForAdvertList,
                                Map<Long, Map<String, ProductPrice>> productPrices) {

        Map<String, Map<String, String>> expiresMap = manageAdsFeatureFormBean.getAdvertFeatureMatrix().getExpires();
        Map<String, Map<String, Boolean>> matrixMap = manageAdsFeatureFormBean.getAdvertFeatureMatrix().getMatrix();
        List<AdPreview> previews = manageAdsFeatureFormBean.getPreviews();
        for (AdPreview preview : previews) {
            ResponsiveAdModel advertModel = createAdvertModel(account, listingCapApi, expiresMap, matrixMap, preview);
            adverts.put(String.valueOf(preview.getId()), advertModel);
        }
        for (AdvertStatisticData data : statisticForAdvertList) {
            setStatisticData(data);
        }

        if(productPrices != null) {
            for (Long adId : productPrices.keySet()) {
                setPrices(productPrices, adId);
            }
        }

        // now copy to array for json string
        advertsList.addAll(adverts.values());
    }

    private boolean isAutoRepostable(Account account, Long categoryId, String advertStatus, String sellerType, ListingCapApi api) {
        if (!account.isPro() &&
                INACTIVE_STATES.contains(advertStatus) &&
                SELLER_TYPE_PRIVATE.equals(sellerType)) {
            ListingCapResponse response = api.getListingCap(categoryId).toBlocking().value();
            // If the advert is in a category that is under listing cap and was posted a seller type `PRIVATE
            // by a non-pro account then the advert needs to be loaded into the SYI page where a new price
            // and listing cap restrictions can be calculated
            return (response == null);
        }
        return true;
    }

    private ResponsiveAdModel createAdvertModel(Account account,
                                                ListingCapApi listingCapApi,
                                                Map<String, Map<String, String>> expiresMap,
                                                Map<String, Map<String, Boolean>> matrixMap,
                                                AdPreview preview) {
        ResponsiveAdModel advertModel = new ResponsiveAdModel();
        advertModel.setAdId(String.valueOf(preview.getId()));
        advertModel.setAge(preview.getCreatedTime());
        advertModel.setMatrix(matrixMap.get(String.valueOf(preview.getId())));
        advertModel.setExpires(expiresMap.get(String.valueOf(preview.getId())));
        advertModel.setRootImgUrl(getRootImgUrl(preview.getPreviewUrl()));
        advertModel.setAdvertUrl(preview.getAdvertUrl());
        advertModel.setAltTag(preview.getAltTag());
        advertModel.setStatus(preview.getStatus().toString());
        advertModel.setDisplayLocationText(preview.getDisplayLocationText());
        advertModel.setDisplayPrice(preview.getDisplayPrice());
        advertModel.setLastModifiedTime(preview.getLastModifiedTime());
        advertModel.setTitle(preview.getTitle());
        advertModel.setEditAdvertUrl(preview.getEditAdvertUrl());
        advertModel.setPostedTime(preview.getPostedTime());
        advertModel.setReadOnlyCategory(preview.getCategoryReadOnly());
        advertModel.setL1CategoryId(preview.getL1CategoryId());
        advertModel.setL2CategoryId(preview.getL2CategoryId());
        advertModel.setL3CategoryId(preview.getL3CategoryId());
        advertModel.setSellerType(preview.getSellerType());
        advertModel.setAutoRepostEnabled( isAutoRepostable(account, preview.getCategoryId(),
                preview.getStatus().name(), preview.getSellerType(), listingCapApi));
        setAdvertModelFlags(preview, advertModel);

        LOGGER.info("Advert AutoRepostEnabled: {} = {}", advertModel.getAdId(), advertModel.isAutoRepostEnabled());

        return advertModel;
    }

    private void setPrices(Map<Long, Map<String, ProductPrice>> productPrices, Long adId) {
        Map<String, ProductPrice> prices = productPrices.get(adId);
        ResponsiveAdModel advertModel = adverts.get(String.valueOf(adId));
        if (advertModel != null) {
            advertModel.setBumpPrice(prices.get(ProductName.BUMP_UP.name()).getPrice().toString());
            advertModel.setUrgentPrice(prices.get(ProductName.URGENT.name()).getPrice().toString());
            advertModel.setTopAd3Price(prices.get(ProductName.FEATURE_3_DAY.name()).getPrice().toString());
            advertModel.setTopAd7Price(prices.get(ProductName.FEATURE_7_DAY.name()).getPrice().toString());
            advertModel.setTopAd14Price(prices.get(ProductName.FEATURE_14_DAY.name()).getPrice().toString());
            advertModel.setSpotlightPrice(prices.get(ProductName.HOMEPAGE_SPOTLIGHT.name()).getPrice().toString());
        }
    }

    private void setAdvertModelFlags(AdPreview preview, ResponsiveAdModel advertModel) {
        // can be spotlight only if it has images
        advertModel.setCanBeHomepageSpotlight(!StringUtils.isBlank(advertModel.getRootImgUrl()));

        // we don't allow bump-up if the ad was published recently
        advertModel.setCanBeBumpedUp(!preview.getPublishedRecently());

        advertModel.setMarkedAsSold(preview.isMarkedAsSold());

        setAdvertStatusFlag(preview, advertModel);
        setStatusAsPerExpiresFlags(advertModel);
        disablePromotionWhenCategoryIsReadOnly(preview, advertModel);
    }

    private void setStatisticData(AdvertStatisticData data) {
        ResponsiveAdModel advertModel = adverts.get(data.getAdvertId());
        if (advertModel != null) {
            advertModel.setViews(String.valueOf(data.getVIPViews()));
            advertModel.setListingViews(String.valueOf(data.getSRPViews()));
            advertModel.setReplies(String.valueOf(data.getNumberOfReplies()));
            advertModel.setBumpTimes(String.valueOf(data.getNumberOfTimesReposted()));
        }
    }

    private void setAdvertStatusFlag(AdPreview preview, ResponsiveAdModel advertModel) {
        ManageAdStatus status = ManageAdStatus.valueOf(preview.getStatus());
        if (ManageAdStatus.EXPIRED.equals(status) ||
                ManageAdStatus.PROCESSING.equals(status) ||
                ManageAdStatus.DELETED.equals(status) ||
                ManageAdStatus.REMOVED.equals(status)
        ) {
            advertModel.setDeletable(false);
            advertModel.setFeaturable(false);
            advertModel.setCanBeUrgent(false);
            advertModel.setCanBeHomepageSpotlight(false);
        }
        if (ManageAdStatus.DELETED.equals(status) ||
                ManageAdStatus.REMOVED.equals(status)) {
            advertModel.setCanBeBumpedUp(false);
        }
        if (ManageAdStatus.DRAFT.equals(status) ||
                ManageAdStatus.PROCESSING.equals(status) ||
                ManageAdStatus.REMOVED.equals(status)
        ) {
            advertModel.setCanBeBumpedUp(false);
        }
        if (ManageAdStatus.REMOVED.equals(status) ||
                ManageAdStatus.PROCESSING.equals(status)) {
            advertModel.setEditable(false);
        }
        if (ManageAdStatus.AWAITING_PHONE_VERIFIED.equals(status)){
            advertModel.setFeaturable(false);
            advertModel.setCanBeUrgent(false);
            advertModel.setCanBeHomepageSpotlight(false);
            advertModel.setCanBeBumpedUp(false);
            advertModel.setEditable(false);
        }
    }

    private void setStatusAsPerExpiresFlags(ResponsiveAdModel advertModel) {
        Map<String, String> expiresFlags = advertModel.getExpires();
        if (expiresFlags != null && !expiresFlags.isEmpty()) {
            if (expiresFlags.containsKey(ProductName.BUMP_UP.toString())) {
                advertModel.setCanBeBumpedUp(false);
            }
            if (expiresFlags.containsKey(ProductName.HOMEPAGE_SPOTLIGHT.toString())) {
                advertModel.setCanBeHomepageSpotlight(false);
            }
            if (expiresFlags.containsKey(ProductName.URGENT.toString())) {
                advertModel.setCanBeUrgent(false);
                advertModel.setUrgent(true);
            }
            if (expiresFlags.containsKey(ProductName.FEATURE_3_DAY.toString()) ||
                    expiresFlags.containsKey(ProductName.FEATURE_7_DAY.toString()) ||
                    expiresFlags.containsKey(ProductName.FEATURE_14_DAY.toString())) {
                advertModel.setFeaturable(false);
                advertModel.setFeatured(true);
            }
        }
    }

    private void disablePromotionWhenCategoryIsReadOnly(AdPreview preview, ResponsiveAdModel advertModel) {
        if (preview.getCategoryReadOnly()) {
            advertModel.setCanBeBumpedUp(false);
            advertModel.setCanBeUrgent(false);
            advertModel.setCanBeHomepageSpotlight(false);
            advertModel.setFeaturable(false);
            advertModel.setEditable(false);
        }
    }

    public static final class Builder {

        private boolean isProUser;
        private boolean isEbayMotorsUser;
        private ManageAdsModel model;
        private ManageAdsFeatureFormBean manageAdsFeatureFormBean;
        private List<AdvertStatisticData> statisticForAdvert;
        private Map<Long, Map<String, ProductPrice>> productPrices;

        // TODO: migrate to model or remove after jsp migration
        private Account account;
        private String accountSuspendedNotice;
        private String accountSuspendedTitle;
        private ManageAdsHelper manageAdsHelper;
        private UrlScheme urlScheme;
        private ListingCapApi listingCapApi;

        @Deprecated
        public Builder(CoreModel.Builder coreBuilder, boolean isProUser) {
            this.isProUser = isProUser;
            this.isEbayMotorsUser = isEbayMotorsUser;
            model = new ManageAdsModel(coreBuilder.build(page()));
            model.isProUser = isProUser;
        }

        public Builder(CoreModel.Builder coreBuilder, boolean isProUser, boolean isEbayMotorsUser) {
            this.isProUser = isProUser;
            this.isEbayMotorsUser = isEbayMotorsUser;
            model = new ManageAdsModel(coreBuilder.build(page()));
            model.isProUser = isProUser;
        }

        public Builder withIsProUser(boolean isProUser) {
            this.isProUser = isProUser;
            return this;
        }

        public Builder withIsEbayMotorsUser(boolean isEbayMotorsUser) {
            this.isEbayMotorsUser = isEbayMotorsUser;
            return this;
        }

        public Builder withManageAdsFeatureFormBean(ManageAdsFeatureFormBean manageAdsFeatureFormBean) {
            this.manageAdsFeatureFormBean = manageAdsFeatureFormBean;
            return this;
        }

        public Builder withStatisticForAdvert(List<AdvertStatisticData> statisticForAdvert) {
            this.statisticForAdvert = statisticForAdvert;
            Map<String, AdvertStatisticData> mapAdsStatistics = Maps.newHashMap();
            for (AdvertStatisticData data : statisticForAdvert) {
                mapAdsStatistics.put(String.valueOf(data.getAdvertId()), data);
            }
            model.adViewStats = mapAdsStatistics;
            return this;
        }

        public Builder withProductPrices(Map<Long, Map<String, ProductPrice>> productPrices) {
            this.productPrices = productPrices;
            model.prices = productPrices;
            return this;
        }

        public Builder withFeatureForm(ManageAdsFeatureFormBean featureForm) {
            model.featureForm = featureForm;
            return this;
        }

        public Builder withFilterForm(ManageAdsFilterFormBean filterForm) {
            model.filterForm = filterForm;
            return this;
        }

        public Builder withAccountSelectionForm(ManageAdsAccountSelectionFormBean accountSelectionForm) {
            model.accountSelectionForm = accountSelectionForm;
            return this;
        }

        public Builder withAccount(Account account) {
            this.account = account;
            return this;
        }

        public Builder withCurrentPageNumber(Integer currentPageNumber) {
            model.currentPageNumber = currentPageNumber;
            return this;
        }

        public Builder withTotalNumberOfPages(Integer totalNumberOfPages) {
            model.totalNumberOfPages = totalNumberOfPages;
            return this;
        }

        public Builder withPaginationSections(List<PaginationSection> paginationSections) {
            model.paginationSections = paginationSections;
            return this;
        }

        public Builder withDecadicPagination(DecadicPagination decadicPagination) {
            model.decadicPagination = decadicPagination;
            return this;
        }

        public Builder withShowDeleteAdBalloon(boolean showDeleteAdBalloon) {
            model.showDeleteAdBalloon = showDeleteAdBalloon;
            return this;
        }

        public Builder withAdvertCount(int advertCount) {
            model.advertCount = advertCount;
            return this;
        }

        public Builder withSearch(String search) {
            model.search = search;
            return this;
        }

        public Builder withEmptySearch(Boolean emptySearch) {
            model.emptySearch = emptySearch != null && emptySearch;
            return this;
        }

        public Builder withClock(Date clock) {
            model.clock = clock;
            return this;
        }

        public Builder withStatus(ManageAdStatus status) {
            model.status = status;
            return this;
        }

        public Builder withBumpupRestrictionMinutes(Integer bumpupRestrictionMinutes) {
            model.bumpupRestrictionMinutes = bumpupRestrictionMinutes;
            return this;
        }

        public Builder withEnableMarketingOptIn(boolean enableMarketingOptIn) {
            model.enableMarketingOptIn = enableMarketingOptIn;
            return this;
        }

        public Builder withShowManageJobsOnMadgexButton(boolean showManageJobsOnMadgexButton) {
            this.model.showManageJobsOnMadgexButton = showManageJobsOnMadgexButton;
            return this;
        }

        public Builder withAccountSuspendedNotice(String accountSuspendedNotice) {
            this.accountSuspendedNotice = accountSuspendedNotice;
            return this;
        }

        public Builder withAccountSuspendedTitle(String accountSuspendedTitle) {
            this.accountSuspendedTitle = accountSuspendedTitle;
            return this;
        }

        public Builder withManageAdsUrls(ManageAdsHelper manageAdsHelper, UrlScheme urlScheme) {
            this.manageAdsHelper = manageAdsHelper;
            this.urlScheme = urlScheme;
            return this;
        }

        public Builder withListingCapApi(ListingCapApi listingCapApi) {
            this.listingCapApi = listingCapApi;
            return this;
        }

        public Builder withError(String error) {
            model.error = error;
            return this;
        }

        public Builder withErrors(ReportableErrorsMessageResolvingErrorSource errors) {
            model.errors = errors;
            return this;
        }

        public Builder withGoogleReviewSwitch(boolean googleReviewSwitch) {
            model.googleReviewSwitch = googleReviewSwitch;
            return this;
        }

        public ModelAndView build() {
            model.populateAdData(account,listingCapApi, manageAdsFeatureFormBean, statisticForAdvert, productPrices);

            ModelAndView modelAndView = new ModelAndView(model.getCore().getPage().getTemplateName());
            modelAndView.addObject(CommonModel.MODEL_KEY, model);

            Map<String, Object> rawModel = modelAndView.getModel();
            rawModel.put("account", account);
            if (accountSuspendedNotice != null) {
                rawModel.put("accountSuspendedNotice", accountSuspendedNotice);
                rawModel.put("accountSuspendedTitle", accountSuspendedTitle);
            }

            manageAdsHelper.addManageAdsUrls(rawModel, new SimpleLink("Manage my ads", urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS)));

            return modelAndView;
        }

        private Page page() {
            final Page page;
            if (isEbayMotorsUser) {
                page = Page.ManageAdsEbayMotorsUser;
            } else if (isProUser) {
                page = Page.ManageAdsPro;
            } else {
                page = Page.ManageAds;
            }
            return page;
        }
    }
}
