package com.gumtree.web.seller.page.payment.model;

import com.gumtree.common.util.json.JsonSerializeUtils;
import com.gumtree.util.model.Link;
import com.gumtree.util.model.SimpleLink;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.manageads.ManageAdsController;
import com.gumtree.web.seller.page.postad.controller.popups.FeaturedExampleController;
import com.gumtree.web.seller.page.postad.controller.popups.SpotlightExampleController;
import com.gumtree.web.seller.page.postad.controller.popups.UrgentExampleController;
import com.gumtree.web.seller.page.postad.model.PostAdFeatureBean;
import com.gumtree.web.seller.page.postad.model.path.PostAdStatePath;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.registration.RegistrationPageController;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

public final class PostAdSuccessViewModel extends CommonModel {
    private String postAdConfirmLink;
    private String invoiceEmail;
    private String paymentAmount;
    private String orderReference;
    private List<String> advertUpdateNotices;
    private String upsellAdvertId;
    private boolean showUpsell;
    private String convertrUrl;
    private PricingMetadata pricingMetadata;
    private String pricingMetadataJson;
    private boolean isNewUser;
    private boolean hasAdvertImage;
    private String l3Category;
    private String l4Category;
    private String adCategories;
    private String advertTitle;
    private String viewUrl;
    // bean is necessary for the model when the user requests
    private PostAdFeatureBean postAdFeatureBean;

    private PostAdSuccessViewModel(CoreModel core) {
        super(core);
    }

    public static Builder builder(CoreModel.Builder coreBuilder, Page page) {
        return new Builder(coreBuilder, page);
    }

    public static final class Builder {
        private final PostAdSuccessViewModel model;

        private Builder(CoreModel.Builder coreBuilder, Page page) {
            model = new PostAdSuccessViewModel(coreBuilder.build(page));
        }

        public Builder withPostAdConfirmLink(String postAdConfirmLink) {
            model.postAdConfirmLink = postAdConfirmLink;
            return this;
        }

        public Builder withInvoiceMail(String invoiceEmail) {
            model.invoiceEmail = invoiceEmail;
            return this;
        }

        public Builder withPaymentAmount(String paymentAmount) {
            model.paymentAmount = paymentAmount;
            return this;
        }

        public Builder withOrderReference(String orderReference) {
            model.orderReference = orderReference;
            return this;
        }

        public Builder withAdvertUpdateNotices(List<String> advertUpdateNotices) {
            model.advertUpdateNotices = advertUpdateNotices;
            return this;
        }

        public Builder withUpsellAdvertId(String upsellAdvertId) {
            model.upsellAdvertId = upsellAdvertId;
            return this;
        }

        public Builder withShowUpsell(boolean showUpsell) {
            model.showUpsell = showUpsell;
            return this;
        }

        public Builder withConvertrUrl(String convertrUrl) {
            model.convertrUrl = convertrUrl;
            return this;
        }

        public Builder withPricingMetadata(PricingMetadata pricingMetadata) {
            model.pricingMetadata = pricingMetadata;
            model.pricingMetadataJson = JsonSerializeUtils.writeToString(new ObjectMapper(), pricingMetadata);
            return this;
        }

        public Builder withIsNewUser(boolean isNewUser) {
            model.isNewUser = isNewUser;
            return this;
        }

        public Builder withHasAdvertImage(boolean hasAdvertImage) {
            model.hasAdvertImage = hasAdvertImage;
            return this;
        }

        public Builder withL3Category(String l3Category) {
            model.l3Category = l3Category;
            return this;
        }

        public Builder withL4Category(String l4Category) {
            model.l4Category = l4Category;
            return this;
        }

        public Builder withAdCategories(String adCategories) {
            model.adCategories = adCategories;
            return this;
        }

        public Builder withPostAdFeatureBean(PostAdFeatureBean postAdFeatureBean) {
            model.postAdFeatureBean = postAdFeatureBean;
            return this;
        }

        public Builder withAdvertTitle(String advertTitle) {
            model.advertTitle = advertTitle;
            return this;
        }

        public Builder withViewUrl(String viewUrl) {
            model.viewUrl = viewUrl;
            return this;
        }

        public ModelAndView build() {
            ModelAndView modelAndView = new ModelAndView(model.getCore().getPage().getTemplateName());
            modelAndView.addObject(CommonModel.MODEL_KEY, model);
            return modelAndView;
        }
    }

    public String getPostAdConfirmLink() {
        return postAdConfirmLink;
    }

    public String getInvoiceEmail() {
        return invoiceEmail;
    }

    public String getPaymentAmount() {
        return paymentAmount;
    }

    public String getOrderReference() {
        return orderReference;
    }

    public String getPostAdAction() {
        return PostAdStatePath.MAPPING;
    }

    public String getManageAdsAction() {
        return ManageAdsController.PAGE_PATH;
    }

    public List<String> getAdvertUpdateNotices() {
        return advertUpdateNotices;
    }

    public boolean isShowUpsell() {
        return showUpsell;
    }

    public String getConvertrUrl() {
        return convertrUrl;
    }

    public String getUpsellAdvertId() {
        return upsellAdvertId;
    }

    public PricingMetadata getPricingMetadata() {
        return pricingMetadata;
    }

    public String getPricingMetadataJson() {
        return pricingMetadataJson;
    }

    public SimpleLink getSpotlightExampleLink() {
        return new SimpleLink("spotlight example", SpotlightExampleController.PAGE_PATH);
    }

    public SimpleLink getFeaturedExampleLink() {
        return new SimpleLink("featured example", FeaturedExampleController.PAGE_PATH);
    }

    public SimpleLink getUrgentExampleLink() {
        return new SimpleLink("urgent example", UrgentExampleController.PAGE_PATH);
    }

    public boolean isNewUser() {
        return isNewUser;
    }

    public boolean isHasAdvertImage() {
        return hasAdvertImage;
    }

    public PostAdFeatureBean getPostAdFeatureBean() {
        return postAdFeatureBean;
    }

    public Link getRegisterLink() {
        return new SimpleLink("create a new account", RegistrationPageController.PAGE_PATH);
    }

    public String getL3Category() {
        return l3Category;
    }

    public String getL4Category() {
        return l4Category;
    }

    public String getAdCategories() {
        return adCategories;
    }

    public String getAdvertTitle() {
        return advertTitle;
    }

    public String getViewUrl() {
        return viewUrl;
    }
}
