package com.gumtree.web.seller.page.manageads.skill.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 技能创建响应类
 */
@Setter
@Getter
public class FacadeSkillCreateResponse {
    private Integer code;
    private boolean success;
    private String msg;
    private SkillData data;

    @Setter
    @Getter
    public static class SkillData {
        private String categoryId;
        private List<Integer> skills;
    }

}
