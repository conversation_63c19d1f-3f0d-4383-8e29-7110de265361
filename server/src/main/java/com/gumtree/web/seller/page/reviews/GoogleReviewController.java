package com.gumtree.web.seller.page.reviews;

import com.google.gson.Gson;
import com.google.gson.JsonNull;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.domain.category.Categories;
import com.gumtree.service.category.CategoryService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.reviews.model.ExternalReviewManagement;
import com.gumtree.web.seller.page.reviews.model.ReviewCommonResponse;
import com.gumtree.web.seller.page.reviews.model.google.AsyncResult;
import com.gumtree.web.seller.page.reviews.model.google.GoogleRelationListResponse;
import com.gumtree.web.seller.page.reviews.model.google.oauth.GoogleOAuthWebRequest;
import com.gumtree.web.seller.page.reviews.service.ExternalReviewsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Optional;

@RestController
@RequestMapping("/review/google")
public class GoogleReviewController extends BaseSellerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(GoogleReviewController.class);

    private final ExternalReviewsService externalReviewsService;

    private final UserSession userSession;

    private final CategoryService categoryService;

    @Autowired
    public GoogleReviewController(
            CookieResolver cookieResolver,
            CategoryModel categoryModel,
            ApiCallExecutor apiCallExecutor,
            ErrorMessageResolver messageResolver,
            UrlScheme urlScheme,
            UserSessionService userSessionService,
            UserSession userSession,
            ExternalReviewsService externalReviewsService, CategoryService categoryService
    ) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.userSession = userSession;
        this.externalReviewsService = externalReviewsService;
        this.categoryService = categoryService;
    }

    @RequestMapping(value = "/auth/route", method = RequestMethod.GET)
    @ResponseBody
    public String route(HttpServletRequest request) {
        return "Success!";
    }

    @RequestMapping(value = {"/get-summary/{category}"}, method = RequestMethod.GET)
    public ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> getSummary(@PathVariable String category) {
        try {
            Long accountId = userSession.getSelectedAccountId();

            if (Optional.of(category)
                    .map(c -> categoryService.getLevelHierarchy(Long.parseLong(c)))
                    .map(e -> e.get(1))
                    .map(e -> Objects.equals(e.getId(), Categories.SERVICES.getId()))
                    .orElse(false)) {
                return new ResponseEntity<>(ReviewCommonResponse.success(externalReviewsService.getSummary(accountId, false)), HttpStatus.OK);
            }
            return new ResponseEntity<>(ReviewCommonResponse.success(null), HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("get-summary error", e);
            return new ResponseEntity<>(ReviewCommonResponse.fail("get-summary error"), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @RequestMapping(value = {"/get-summary"}, method = RequestMethod.GET)
    public ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> getSummary() {
        try {
            Long accountId = userSession.getSelectedAccountId();
            return new ResponseEntity<>(ReviewCommonResponse.success(externalReviewsService.getSummary(accountId, true)), HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("get-summary error", e);
            return new ResponseEntity<>(ReviewCommonResponse.fail("get-summary error"), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @RequestMapping(value = "/do-auth", method = RequestMethod.POST, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public ResponseEntity<ReviewCommonResponse<AsyncResult>> doAuth(@ModelAttribute GoogleOAuthWebRequest request) {
        try {
            Long selectedAccountId = userSession.getSelectedAccountId();
            return new ResponseEntity<>(ReviewCommonResponse.success(externalReviewsService.doGoogleAuth(selectedAccountId, request)), HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.warn("do-auth error", e);
            return new ResponseEntity<>(ReviewCommonResponse.fail("do-auth error"), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @RequestMapping(value = "/set-display/{relationId}/{display}", method = RequestMethod.POST)
    public ResponseEntity<ReviewCommonResponse<String>> setDisplay(
            @PathVariable String relationId,
            @PathVariable Integer display) {
        try {
            Long selectedAccountId = userSession.getSelectedAccountId();
            return new ResponseEntity<>(ReviewCommonResponse.success(externalReviewsService.setGoogleDisplay(selectedAccountId, relationId, display)), HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("set-display error", e);
            return new ResponseEntity<>(ReviewCommonResponse.fail("set-display error"), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @RequestMapping(value = "/re-sync/{authid}", method = RequestMethod.POST)
    public ResponseEntity<ReviewCommonResponse<AsyncResult>> reSync(@PathVariable String authid) {
        try {
            Long selectedAccountId = userSession.getSelectedAccountId();
            return new ResponseEntity<>(ReviewCommonResponse.success(externalReviewsService.reSyncGoogleData(selectedAccountId, authid)), HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("re-sync error", e);
            return new ResponseEntity<>(ReviewCommonResponse.fail("re-sync error"), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @RequestMapping(value = "/get-relation-list/{authid}", method = RequestMethod.GET)
    public ResponseEntity<ReviewCommonResponse<GoogleRelationListResponse>> getRelationList(@PathVariable String authid) {
        try {
            Long selectedAccountId = userSession.getSelectedAccountId();
            return new ResponseEntity<>(ReviewCommonResponse.success(externalReviewsService.getGoogleRelationList(selectedAccountId, authid)), HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("get-relation-list error", e);
            return new ResponseEntity<>(ReviewCommonResponse.fail("get-relation-list error"), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @RequestMapping(value = "/set-relation/{relationId}", method = RequestMethod.POST)
    public ResponseEntity<ReviewCommonResponse<String>> setRelation(@PathVariable String relationId) {
        try {
            Long selectedAccountId = userSession.getSelectedAccountId();
            return new ResponseEntity<>(ReviewCommonResponse.success(externalReviewsService.setGoogleRelation(selectedAccountId, relationId)), HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("set-relation error", e);
            return new ResponseEntity<>(ReviewCommonResponse.fail("set-relation error"), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @RequestMapping(value = "/unbind/{authid}", method = RequestMethod.POST)
    public ResponseEntity<ReviewCommonResponse<AsyncResult>> unBind(@PathVariable String authid) {
        try {
            Long selectedAccountId = userSession.getSelectedAccountId();
            return new ResponseEntity<>(ReviewCommonResponse.success(externalReviewsService.unBind(selectedAccountId, authid)), HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("un-bind error", e);
            return new ResponseEntity<>(ReviewCommonResponse.fail("un-bind error"), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
