package com.gumtree.web.seller.page.password.api;

import com.gumtree.bapi.UserApi;
import com.gumtree.bapi.model.PasswordKey;
import com.gumtree.bapi.model.ResetPasswordBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
public class UserApiServiceImpl implements UserApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserApiServiceImpl.class);
    private final UserApi userApi;

    @Autowired
    public UserApiServiceImpl(@Qualifier(value = "bapiContractUserApi") UserApi userApi){
        this.userApi = userApi;
    }

    @Override
    public PasswordKey getPasswordKey(String key) {
        ResetPasswordBean resetPasswordBean = new ResetPasswordBean().key(key);
        return userApi.getPasswordKey(resetPasswordBean)
                .onErrorReturn(error -> {
                    LOGGER.warn("Error getting reset password key : ", error);
                    return null;
                })
                .toBlocking().value();
    }
}
