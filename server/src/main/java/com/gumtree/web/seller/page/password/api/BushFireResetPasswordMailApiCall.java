package com.gumtree.web.seller.page.password.api;

import com.gumtree.api.PasswordKey;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.client.executor.ApiCall;

/**
 *
 */
public class BushFireResetPasswordMailApiCall implements ApiCall<PasswordKey> {

    private String username;

    /**
     * Call to send password reset email
     * @param username The user
     */
    public BushFireResetPasswordMailApiCall(String username) {
        this.username = username;
    }

    @Override
    public final PasswordKey execute(BushfireApi api) {
        api.create(UserApi.class).initiatePasswordReset(username);
        return null;
    }
}
